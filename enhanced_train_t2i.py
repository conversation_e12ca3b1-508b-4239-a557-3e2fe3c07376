#!/usr/bin/env python3
"""
增强版训练脚本，添加SSIM和PSNR指标的实时计算和输出
基于原始的autoregressive/train/train_t2i.py，增加了图像质量评估功能
"""

import torch
import torch.nn.functional as F
import numpy as np
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
import cv2
from PIL import Image
import torchvision.transforms as transforms
import wandb
import logging
import os
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import json
import time
from tqdm import tqdm


def calculate_ssim_psnr(generated_images, target_images):
    """
    计算生成图像和目标图像之间的SSIM和PSNR
    
    Args:
        generated_images: 生成的图像张量 (B, C, H, W)
        target_images: 目标图像张量 (B, C, H, W)
    
    Returns:
        ssim_scores: SSIM分数列表
        psnr_scores: PSNR分数列表
    """
    ssim_scores = []
    psnr_scores = []
    
    # 确保图像在正确的范围内 [0, 1]
    generated_images = torch.clamp(generated_images, 0, 1)
    target_images = torch.clamp(target_images, 0, 1)
    
    # 转换为numpy数组
    gen_np = generated_images.detach().cpu().numpy()
    target_np = target_images.detach().cpu().numpy()
    
    batch_size = gen_np.shape[0]
    
    for i in range(batch_size):
        # 转换为 (H, W, C) 格式
        gen_img = np.transpose(gen_np[i], (1, 2, 0))
        target_img = np.transpose(target_np[i], (1, 2, 0))
        
        # 如果是RGB图像，转换为灰度图像进行SSIM计算
        if gen_img.shape[2] == 3:
            gen_gray = cv2.cvtColor((gen_img * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            target_gray = cv2.cvtColor((target_img * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            
            # 计算SSIM
            ssim_score = ssim(target_gray, gen_gray, data_range=255)
        else:
            # 灰度图像
            gen_gray = (gen_img.squeeze() * 255).astype(np.uint8)
            target_gray = (target_img.squeeze() * 255).astype(np.uint8)
            ssim_score = ssim(target_gray, gen_gray, data_range=255)
        
        # 计算PSNR
        psnr_score = psnr(target_img, gen_img, data_range=1.0)
        
        ssim_scores.append(ssim_score)
        psnr_scores.append(psnr_score)
    
    return ssim_scores, psnr_scores


def log_image_metrics(logger, ssim_scores, psnr_scores, step, prefix="eval"):
    """
    记录图像质量指标到日志和wandb
    
    Args:
        logger: 日志记录器
        ssim_scores: SSIM分数列表
        psnr_scores: PSNR分数列表
        step: 当前步数
        prefix: 日志前缀
    """
    if len(ssim_scores) == 0 or len(psnr_scores) == 0:
        return
    
    avg_ssim = np.mean(ssim_scores)
    avg_psnr = np.mean(psnr_scores)
    std_ssim = np.std(ssim_scores)
    std_psnr = np.std(psnr_scores)
    
    # 记录到日志
    logger.info(f"(step={step:07d}) {prefix.upper()} SSIM: {avg_ssim:.4f}±{std_ssim:.4f}, PSNR: {avg_psnr:.2f}±{std_psnr:.2f}")
    
    # 记录到wandb
    if torch.distributed.get_rank() == 0:
        wandb_log = {
            f'{prefix}_ssim_mean': avg_ssim,
            f'{prefix}_ssim_std': std_ssim,
            f'{prefix}_psnr_mean': avg_psnr,
            f'{prefix}_psnr_std': std_psnr,
            f'{prefix}_ssim_min': np.min(ssim_scores),
            f'{prefix}_ssim_max': np.max(ssim_scores),
            f'{prefix}_psnr_min': np.min(psnr_scores),
            f'{prefix}_psnr_max': np.max(psnr_scores),
        }
        wandb.log(wandb_log, step=step)


def tensor_to_pil(tensor):
    """将tensor转换为PIL图像"""
    # 确保tensor在[0,1]范围内
    tensor = torch.clamp(tensor, 0, 1)
    # 转换为numpy数组
    if tensor.dim() == 4:  # batch dimension
        tensor = tensor[0]  # 取第一个
    if tensor.dim() == 3:  # (C, H, W)
        tensor = tensor.permute(1, 2, 0)  # (H, W, C)

    # 转换为numpy并缩放到0-255
    np_img = (tensor.detach().cpu().numpy() * 255).astype(np.uint8)

    # 如果是灰度图，转换为RGB
    if np_img.shape[2] == 1:
        np_img = np.repeat(np_img, 3, axis=2)

    return Image.fromarray(np_img)


def create_comparison_image(source_img, target_img, generated_img, ssim_score, psnr_score, save_path):
    """创建源图像、目标图像、生成图像的对比图"""
    try:
        # 转换tensor为PIL图像
        source_pil = tensor_to_pil(source_img)
        target_pil = tensor_to_pil(target_img)
        generated_pil = tensor_to_pil(generated_img)

        # 创建对比图
        fig = plt.figure(figsize=(15, 5))
        gs = gridspec.GridSpec(1, 3, figure=fig)

        # 源图像 (BUS)
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.imshow(source_pil)
        ax1.set_title('源图像 (BUS)', fontsize=12, fontweight='bold')
        ax1.axis('off')

        # 目标图像 (CEUS)
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.imshow(target_pil)
        ax2.set_title('目标图像 (CEUS)', fontsize=12, fontweight='bold')
        ax2.axis('off')

        # 生成图像
        ax3 = fig.add_subplot(gs[0, 2])
        ax3.imshow(generated_pil)
        ax3.set_title(f'生成图像\nSSIM: {ssim_score:.4f}\nPSNR: {psnr_score:.2f}dB',
                     fontsize=12, fontweight='bold')
        ax3.axis('off')

        # 添加整体标题
        fig.suptitle('BUS到CEUS图像转换对比', fontsize=16, fontweight='bold', y=0.95)

        # 调整布局
        plt.tight_layout()
        plt.subplots_adjust(top=0.85)

        # 保存图像
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return True

    except Exception as e:
        print(f"创建对比图失败: {e}")
        return False


def enhanced_evaluation_loop(eval_model, vq_model, val_loader, args, device, ptdtype,
                           latent_size, train_steps, checkpoint_dir, logger):
    """
    增强版评估循环，添加SSIM和PSNR计算
    """
    from autoregressive.models.generate import generate
    from torchvision.utils import save_image
    
    all_ssim_scores = []
    all_psnr_scores = []
    
    eval_model.eval()
    
    for eval_idx, patch in enumerate(tqdm(val_loader, desc="Evaluating")):
        if args.subject_driven:
            (eval_x, eval_pixel_values, eval_c_indices, eval_cond_attn_mask, 
             eval_image_masks, eval_gt_img, eval_valid, pixual_value_source, 
             text_input_ids, text_attention_mask), is_generated = patch
            
            if text_input_ids is not None:
                text_input_ids = text_input_ids.to(device, non_blocking=True)
                text_attention_mask = text_attention_mask.to(device, non_blocking=True)
        else:
            (eval_x, eval_pixel_values, eval_c_indices, eval_cond_attn_mask, 
             eval_image_masks, eval_gt_img, eval_valid, pixual_value_source) = patch
            text_input_ids = None
            text_attention_mask = None
        
        # 移动数据到设备
        if eval_pixel_values is not None:
            eval_pixel_values = eval_pixel_values.to(device, ptdtype, non_blocking=True)
            eval_image_masks = eval_image_masks.to(device, ptdtype, non_blocking=True)
        
        eval_x = eval_x.to(device, ptdtype, non_blocking=True)
        eval_c_indices = eval_c_indices.to(device, non_blocking=True)
        eval_cond_attn_mask = eval_cond_attn_mask.to(device, non_blocking=True)
        eval_gt_img = eval_gt_img.to(device, ptdtype, non_blocking=True)
        
        # 获取多模态嵌入
        caption_embs = eval_model.get_multmodal_embeddings(
            pixel_values=eval_pixel_values,
            cond_idx=eval_c_indices,
            cond_idx_mask=eval_cond_attn_mask,
            img_mask=eval_image_masks,
            text_input_ids=text_input_ids,
            text_attention_mask=text_attention_mask
        )
        
        emb_masks = eval_cond_attn_mask
        
        # 处理左填充
        if not args.no_left_padding:
            new_emb_masks = torch.flip(emb_masks, dims=[-1])
            new_caption_embs = []
            for caption_emb, emb_mask in zip(caption_embs, emb_masks):
                valid_num = int(emb_mask.sum().item())
                new_caption_emb = torch.cat([caption_emb[valid_num:], caption_emb[:valid_num]])
                new_caption_embs.append(new_caption_emb)
            new_caption_embs = torch.stack(new_caption_embs)
        else:
            new_caption_embs, new_emb_masks = caption_embs, emb_masks
        
        new_c_indices = new_caption_embs * new_emb_masks[:, :, None]
        c_emb_masks = new_emb_masks
        
        qzshape = [len(new_c_indices), args.codebook_embed_dim, latent_size, latent_size]
        
        # 生成图像
        index_sample = generate(
            eval_model, new_c_indices, latent_size ** 2,
            c_emb_masks,
            cfg_scale=args.cfg_scale,
            temperature=args.temperature, 
            top_k=args.top_k,
            top_p=args.top_p, 
            sample_logits=True,
        )
        
        # 解码生成的图像
        img_samples = vq_model.decode_code(index_sample, qzshape)
        
        # 归一化到 [0, 1] 范围
        img_samples_normalized = (img_samples + 1) / 2
        img_samples_normalized = torch.clamp(img_samples_normalized, 0, 1)
        
        eval_gt_normalized = (eval_gt_img + 1) / 2
        eval_gt_normalized = torch.clamp(eval_gt_normalized, 0, 1)
        
        # 计算SSIM和PSNR
        batch_ssim, batch_psnr = calculate_ssim_psnr(img_samples_normalized, eval_gt_normalized)
        all_ssim_scores.extend(batch_ssim)
        all_psnr_scores.extend(batch_psnr)
        
        # 保存评估图像和对比图
        eval_dir = f"{checkpoint_dir}/eval_step_{train_steps}"
        os.makedirs(eval_dir, exist_ok=True)

        # 保存前几个batch的详细对比图
        if eval_idx < 3:  # 保存前3个batch的对比图
            sample_save_path = f"{eval_dir}/batch_{eval_idx}_cfg_{args.cfg_scale}_topk_{args.top_k}.jpg"

            # 准备要保存的图像
            if eval_pixel_values is not None:
                source_images = (eval_pixel_values + 1) / 2
                source_images = torch.clamp(source_images, 0, 1)
                images_to_save = torch.cat((source_images, img_samples_normalized, eval_gt_normalized), dim=0)
            else:
                images_to_save = torch.cat((img_samples_normalized, eval_gt_normalized), dim=0)

            save_image(images_to_save, sample_save_path, nrow=img_samples.shape[0], normalize=False)

            # 为每个图像创建详细的对比图
            for img_idx in range(min(img_samples_normalized.shape[0], 3)):  # 最多保存3张对比图
                if eval_pixel_values is not None:
                    source_img = source_images[img_idx]
                    target_img = eval_gt_normalized[img_idx]
                    generated_img = img_samples_normalized[img_idx]
                    ssim_score = batch_ssim[img_idx] if img_idx < len(batch_ssim) else 0
                    psnr_score = batch_psnr[img_idx] if img_idx < len(batch_psnr) else 0

                    comparison_save_path = f"{eval_dir}/comparison_batch_{eval_idx}_img_{img_idx}.png"

                    # 创建对比图
                    success = create_comparison_image(
                        source_img, target_img, generated_img,
                        ssim_score, psnr_score, comparison_save_path
                    )

                    if success:
                        logger.info(f"保存对比图: {comparison_save_path}")

                    # 记录单张图像的指标到文件
                    metrics_file = f"{eval_dir}/metrics_batch_{eval_idx}_img_{img_idx}.txt"
                    with open(metrics_file, 'w') as f:
                        f.write(f"Image {img_idx} Metrics (Batch {eval_idx})\n")
                        f.write("=" * 40 + "\n")
                        f.write(f"SSIM: {ssim_score:.4f}\n")
                        f.write(f"PSNR: {psnr_score:.2f} dB\n")
                        f.write(f"Training Step: {train_steps}\n")
        
        # 每处理几个batch输出一次中间结果
        if (eval_idx + 1) % 10 == 0:
            current_ssim = np.mean(all_ssim_scores)
            current_psnr = np.mean(all_psnr_scores)
            logger.info(f"Eval progress: {eval_idx+1}/{len(val_loader)}, "
                       f"Current SSIM: {current_ssim:.4f}, Current PSNR: {current_psnr:.2f}")
    
    # 记录最终指标
    log_image_metrics(logger, all_ssim_scores, all_psnr_scores, train_steps, "eval")
    
    return all_ssim_scores, all_psnr_scores


def save_metrics_to_file(checkpoint_dir, train_steps, ssim_scores, psnr_scores):
    """
    将指标保存到文件
    """
    metrics_file = f"{checkpoint_dir}/metrics_step_{train_steps}.txt"
    
    with open(metrics_file, 'w') as f:
        f.write(f"Evaluation Metrics for Step {train_steps}\n")
        f.write("=" * 50 + "\n")
        f.write(f"SSIM - Mean: {np.mean(ssim_scores):.4f}, Std: {np.std(ssim_scores):.4f}\n")
        f.write(f"SSIM - Min: {np.min(ssim_scores):.4f}, Max: {np.max(ssim_scores):.4f}\n")
        f.write(f"PSNR - Mean: {np.mean(psnr_scores):.2f}, Std: {np.std(psnr_scores):.2f}\n")
        f.write(f"PSNR - Min: {np.min(psnr_scores):.2f}, Max: {np.max(psnr_scores):.2f}\n")
        f.write("\nDetailed Scores:\n")
        f.write("Image_ID,SSIM,PSNR\n")
        for i, (ssim_val, psnr_val) in enumerate(zip(ssim_scores, psnr_scores)):
            f.write(f"{i},{ssim_val:.4f},{psnr_val:.2f}\n")


# 这个函数将被集成到原始的训练脚本中
def integrate_metrics_to_training():
    """
    集成指标计算到训练循环的说明
    """
    print("""
    要在训练中集成SSIM和PSNR指标，需要在原始训练脚本的评估部分添加以下代码：
    
    1. 在评估循环中调用 enhanced_evaluation_loop 函数
    2. 在每个评估步骤记录指标
    3. 保存指标到文件和wandb
    
    具体集成位置：autoregressive/train/train_t2i.py 第675行附近的评估代码块
    """)


if __name__ == "__main__":
    integrate_metrics_to_training()
