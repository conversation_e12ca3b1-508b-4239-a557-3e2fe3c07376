#!/usr/bin/env python3
"""
可视化对比图查看工具
帮助用户快速浏览和分析训练过程中生成的对比图
"""

import os
import glob
import argparse
import re
from pathlib import Path
import webbrowser
import tempfile


def find_comparison_images(checkpoint_dir):
    """查找所有对比图文件"""
    pattern = os.path.join(checkpoint_dir, "*/comparison_*.png")
    comparison_files = glob.glob(pattern)
    
    # 解析文件信息
    image_info = []
    for file_path in comparison_files:
        # 提取信息：comparison_batch_0_img_0.png
        filename = os.path.basename(file_path)
        match = re.search(r'comparison_batch_(\d+)_img_(\d+)\.png', filename)
        if match:
            batch_idx = int(match.group(1))
            img_idx = int(match.group(2))
            
            # 提取步数
            step_match = re.search(r'eval_step_(\d+)', file_path)
            step = int(step_match.group(1)) if step_match else 0
            
            image_info.append({
                'path': file_path,
                'step': step,
                'batch': batch_idx,
                'img': img_idx,
                'filename': filename
            })
    
    # 按步数、batch、图像索引排序
    image_info.sort(key=lambda x: (x['step'], x['batch'], x['img']))
    return image_info


def get_metrics_for_image(checkpoint_dir, step, batch, img):
    """获取特定图像的指标数据"""
    metrics_file = f"{checkpoint_dir}/eval_step_{step}/metrics_batch_{batch}_img_{img}.txt"
    
    if os.path.exists(metrics_file):
        try:
            with open(metrics_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取SSIM和PSNR
            ssim_match = re.search(r'SSIM: ([\d.]+)', content)
            psnr_match = re.search(r'PSNR: ([\d.]+)', content)
            
            ssim = float(ssim_match.group(1)) if ssim_match else 0
            psnr = float(psnr_match.group(1)) if psnr_match else 0
            
            return ssim, psnr
        except:
            pass
    
    return 0, 0


def create_html_gallery(image_info, checkpoint_dir, output_file):
    """创建HTML图库"""
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BUS到CEUS训练对比图库</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        .image-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .image-card:hover {
            transform: translateY(-5px);
        }
        .image-card img {
            width: 100%;
            height: auto;
            border-radius: 5px;
            cursor: pointer;
        }
        .image-info {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .metrics {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
        }
        .metric {
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .step-group {
            margin-bottom: 40px;
        }
        .step-header {
            background: #343a40;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }
        .modal-content {
            margin: auto;
            display: block;
            width: 90%;
            max-width: 1200px;
            margin-top: 50px;
        }
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }
        .filter-controls {
            margin-bottom: 20px;
            text-align: center;
        }
        .filter-controls select, .filter-controls input {
            margin: 5px;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔬 BUS到CEUS训练对比图库</h1>
        <p>训练步数范围: {step_range} | 总图像数: {total_images}</p>
    </div>
    
    <div class="filter-controls">
        <label>筛选步数: </label>
        <select id="stepFilter" onchange="filterImages()">
            <option value="">所有步数</option>
            {step_options}
        </select>
        
        <label>最小SSIM: </label>
        <input type="number" id="ssimFilter" step="0.01" min="0" max="1" placeholder="0.0" onchange="filterImages()">
        
        <label>最小PSNR: </label>
        <input type="number" id="psnrFilter" step="0.1" min="0" max="50" placeholder="0.0" onchange="filterImages()">
    </div>
    
    <div id="gallery">
        {gallery_content}
    </div>
    
    <!-- 模态框 -->
    <div id="imageModal" class="modal" onclick="closeModal()">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>
    
    <script>
        function openModal(src) {
            document.getElementById('imageModal').style.display = 'block';
            document.getElementById('modalImage').src = src;
        }
        
        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }
        
        function filterImages() {
            const stepFilter = document.getElementById('stepFilter').value;
            const ssimFilter = parseFloat(document.getElementById('ssimFilter').value) || 0;
            const psnrFilter = parseFloat(document.getElementById('psnrFilter').value) || 0;
            
            const cards = document.querySelectorAll('.image-card');
            cards.forEach(card => {
                const step = card.dataset.step;
                const ssim = parseFloat(card.dataset.ssim);
                const psnr = parseFloat(card.dataset.psnr);
                
                const stepMatch = !stepFilter || step === stepFilter;
                const ssimMatch = ssim >= ssimFilter;
                const psnrMatch = psnr >= psnrFilter;
                
                if (stepMatch && ssimMatch && psnrMatch) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
"""
    
    # 按步数分组
    steps = sorted(set(img['step'] for img in image_info))
    step_range = f"{min(steps)} - {max(steps)}" if steps else "无"
    total_images = len(image_info)
    
    # 生成步数选项
    step_options = ""
    for step in steps:
        step_options += f'<option value="{step}">Step {step}</option>\n'
    
    # 生成图库内容
    gallery_content = ""
    current_step = None
    
    for img in image_info:
        # 获取指标
        ssim, psnr = get_metrics_for_image(checkpoint_dir, img['step'], img['batch'], img['img'])
        
        # 如果是新的步数，添加步数标题
        if current_step != img['step']:
            if current_step is not None:
                gallery_content += "</div>\n"  # 关闭上一个步数组
            gallery_content += f"""
            <div class="step-group">
                <div class="step-header">训练步数: {img['step']}</div>
                <div class="gallery">
            """
            current_step = img['step']
        
        # 转换文件路径为相对路径
        rel_path = os.path.relpath(img['path'], os.path.dirname(output_file))
        
        gallery_content += f"""
        <div class="image-card" data-step="{img['step']}" data-ssim="{ssim}" data-psnr="{psnr}">
            <img src="{rel_path}" alt="对比图" onclick="openModal('{rel_path}')">
            <div class="image-info">
                <strong>Batch {img['batch']}, Image {img['img']}</strong>
                <div class="metrics">
                    <span class="metric">SSIM: {ssim:.4f}</span>
                    <span class="metric">PSNR: {psnr:.2f}dB</span>
                </div>
            </div>
        </div>
        """
    
    if current_step is not None:
        gallery_content += "</div></div>\n"  # 关闭最后一个步数组
    
    # 替换模板内容
    html_content = html_content.format(
        step_range=step_range,
        total_images=total_images,
        step_options=step_options,
        gallery_content=gallery_content
    )
    
    # 写入HTML文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)


def main():
    parser = argparse.ArgumentParser(description="查看BUS到CEUS训练对比图")
    parser.add_argument("--checkpoint_dir", type=str, default="checkpoints/bus_to_ceus_stage2",
                       help="检查点目录")
    parser.add_argument("--output", type=str, default="comparison_gallery.html",
                       help="输出HTML文件名")
    parser.add_argument("--open_browser", action="store_true", default=True,
                       help="自动打开浏览器")
    
    args = parser.parse_args()
    
    # 检查输入目录
    if not os.path.exists(args.checkpoint_dir):
        print(f"❌ 检查点目录不存在: {args.checkpoint_dir}")
        return
    
    print(f"🔍 搜索对比图: {args.checkpoint_dir}")
    
    # 查找对比图
    image_info = find_comparison_images(args.checkpoint_dir)
    
    if not image_info:
        print("❌ 没有找到对比图文件")
        print("请确保训练时启用了SSIM/PSNR计算功能")
        return
    
    print(f"✅ 找到 {len(image_info)} 张对比图")
    
    # 统计信息
    steps = sorted(set(img['step'] for img in image_info))
    print(f"📊 训练步数: {steps}")
    
    # 创建HTML图库
    print(f"🎨 生成HTML图库: {args.output}")
    create_html_gallery(image_info, args.checkpoint_dir, args.output)
    
    print(f"✅ HTML图库已生成: {args.output}")
    
    # 打开浏览器
    if args.open_browser:
        file_url = f"file://{os.path.abspath(args.output)}"
        webbrowser.open(file_url)
        print(f"🌐 已在浏览器中打开: {file_url}")
    
    print("\n📋 使用说明:")
    print("- 点击图像可放大查看")
    print("- 使用筛选器按步数或指标过滤图像")
    print("- 每张图像显示对应的SSIM和PSNR数值")


if __name__ == "__main__":
    main()
