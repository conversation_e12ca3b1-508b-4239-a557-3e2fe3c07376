#!/bin/bash
# BUS到CEUS转换 - Stage 1训练脚本
# 用于多模态对齐和图像重建任务

set -e  # 遇到错误时退出

# 配置GPU
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export PYTHONPATH="$(pwd)":$PYTHONPATH
export NCCL_P2P_DISABLE=1 
export NCCL_IB_DISABLE=1 

# 实验配置
experiment_name="checkpoints/bus_to_ceus_stage1"
model_name_or_path="models/blip2-flan-t5-xl"

# 数据路径 - 使用我们准备的数据
data_path="data/train_stage1.jsonl"
val_data_path="data/valid.jsonl"

# 模型路径
load_from_checkpoint="models/Mentor/generator_ckpt.pt"
language_projection="llava-v1.5-flant5_fixed-pretrain/mm_projector.bin"
vq_ckpt="models/Mentor/vq_ds16_t2i.pt"

# 训练参数
lr=5e-4
num_workers=4
nproc_per_node=1  # 根据您的GPU数量调整
eval_steps=500    # 更频繁的评估，因为医学图像数据通常较少
ckpt_every=500    # 更频繁的保存
multimodal_encoder="llava"

# 检查必要文件是否存在
echo "检查必要文件..."

if [ ! -f "$data_path" ]; then
    echo "错误: 训练数据文件不存在: $data_path"
    echo "请先运行: python prepare_bus_ceus_data.py --train_dir train --output_dir data"
    exit 1
fi

if [ ! -f "$load_from_checkpoint" ]; then
    echo "错误: 预训练模型不存在: $load_from_checkpoint"
    echo "请先运行: python setup_environment.py --download_models"
    exit 1
fi

if [ ! -f "$vq_ckpt" ]; then
    echo "错误: VQ模型不存在: $vq_ckpt"
    echo "请先运行: python setup_environment.py --download_models"
    exit 1
fi

# 创建输出目录
mkdir -p "$experiment_name"
mkdir -p logs

echo "开始Stage 1训练: BUS到CEUS图像转换"
echo "实验名称: $experiment_name"
echo "训练数据: $data_path"
echo "验证数据: $val_data_path"

# 启动训练
torchrun \
--nnodes=1 \
--nproc_per_node=$nproc_per_node \
autoregressive/train/train_t2i.py \
--vq-ckpt "$vq_ckpt" \
--data-path "$data_path" \
--dataset ti2i \
--image-size 512 \
--results-dir "$experiment_name" \
--cloud-save-path checkpoint \
--lr $lr \
--val_data_path "$val_data_path" \
--use_vision_tower \
--model_name_or_path "$model_name_or_path" \
--image_place_holder "<image>" \
--do_eval \
--eval_steps $eval_steps \
--max_eval_samples 100 \
--cfg-scale 7.5 \
--top-k 16384 \
--load_from_checkpoint "$load_from_checkpoint" \
--global-batch-size 32 \
--num-workers $num_workers \
--warmup 0.05 \
--gradient-accumulation-steps 8 \
--train_text_encoder \
--ckpt-every $ckpt_every \
--epochs 3 \
--subject_driven \
--multimodal_encoder $multimodal_encoder \
--find_unused_parameters \
--cls-token-num 512 \
--load_language_projection "$language_projection" \
--mm_vision_tower "openai/clip-vit-large-patch14" \
--save_total_limit 3 \
--load_fixed_llamagen \
--unfreeze_output \
--with_image_only \
--image_only_rate 0.1 \
--fix 'gpt-empty-fix' \
--do_recovery \
2>&1 | tee "logs/stage1_$(date +%Y%m%d_%H%M%S).log"

echo "Stage 1训练完成！"
echo "检查点保存在: $experiment_name"
echo "日志保存在: logs/"
echo ""
echo "下一步: 运行Stage 2训练"
echo "bash train_bus_to_ceus_stage2.sh"
