#!/usr/bin/env python3
"""
训练指标分析工具
分析Stage 2训练过程中的SSIM和PSNR指标，帮助选择最佳模型
"""

import os
import re
import glob
import argparse
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path


def parse_metrics_file(file_path):
    """解析指标文件，提取SSIM和PSNR数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取步数
        step_match = re.search(r'step_(\d+)', file_path)
        step = int(step_match.group(1)) if step_match else 0
        
        # 提取SSIM指标
        ssim_match = re.search(r'SSIM - Mean: ([\d.]+), Std: ([\d.]+)', content)
        ssim_mean = float(ssim_match.group(1)) if ssim_match else 0
        ssim_std = float(ssim_match.group(2)) if ssim_match else 0
        
        # 提取PSNR指标
        psnr_match = re.search(r'PSNR - Mean: ([\d.]+), Std: ([\d.]+)', content)
        psnr_mean = float(psnr_match.group(1)) if psnr_match else 0
        psnr_std = float(psnr_match.group(2)) if psnr_match else 0
        
        return {
            'step': step,
            'ssim_mean': ssim_mean,
            'ssim_std': ssim_std,
            'psnr_mean': psnr_mean,
            'psnr_std': psnr_std
        }
    except Exception as e:
        print(f"解析文件失败 {file_path}: {e}")
        return None


def collect_all_metrics(checkpoint_dir):
    """收集所有指标文件的数据"""
    metrics_files = glob.glob(os.path.join(checkpoint_dir, "*/metrics_step_*.txt"))
    
    if not metrics_files:
        print(f"在 {checkpoint_dir} 中找不到指标文件")
        return []
    
    metrics_data = []
    for file_path in sorted(metrics_files):
        data = parse_metrics_file(file_path)
        if data:
            metrics_data.append(data)
    
    return sorted(metrics_data, key=lambda x: x['step'])


def calculate_composite_score(ssim, psnr, ssim_weight=0.6):
    """计算综合评分"""
    # 归一化PSNR到0-1范围（假设最大值为40dB）
    psnr_normalized = min(psnr / 40.0, 1.0)
    return ssim_weight * ssim + (1 - ssim_weight) * psnr_normalized


def find_best_model(metrics_data):
    """找到最佳模型"""
    if not metrics_data:
        return None
    
    best_model = None
    best_score = -1
    
    for data in metrics_data:
        score = calculate_composite_score(data['ssim_mean'], data['psnr_mean'])
        if score > best_score:
            best_score = score
            best_model = data
    
    return best_model, best_score


def plot_metrics(metrics_data, output_dir):
    """绘制指标趋势图"""
    if not metrics_data:
        print("没有数据可绘制")
        return
    
    steps = [d['step'] for d in metrics_data]
    ssim_means = [d['ssim_mean'] for d in metrics_data]
    ssim_stds = [d['ssim_std'] for d in metrics_data]
    psnr_means = [d['psnr_mean'] for d in metrics_data]
    psnr_stds = [d['psnr_std'] for d in metrics_data]
    
    # 创建图表
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
    
    # SSIM趋势
    ax1.errorbar(steps, ssim_means, yerr=ssim_stds, marker='o', capsize=5)
    ax1.set_title('SSIM趋势 (结构相似性指数)')
    ax1.set_xlabel('训练步数')
    ax1.set_ylabel('SSIM')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 1)
    
    # PSNR趋势
    ax2.errorbar(steps, psnr_means, yerr=psnr_stds, marker='s', color='orange', capsize=5)
    ax2.set_title('PSNR趋势 (峰值信噪比)')
    ax2.set_xlabel('训练步数')
    ax2.set_ylabel('PSNR (dB)')
    ax2.grid(True, alpha=0.3)
    
    # 综合评分
    scores = [calculate_composite_score(s, p) for s, p in zip(ssim_means, psnr_means)]
    ax3.plot(steps, scores, marker='^', color='green', linewidth=2)
    ax3.set_title('综合评分趋势 (0.6×SSIM + 0.4×PSNR_norm)')
    ax3.set_xlabel('训练步数')
    ax3.set_ylabel('综合评分')
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(0, 1)
    
    plt.tight_layout()
    
    # 保存图表
    plot_path = os.path.join(output_dir, 'training_metrics.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"✅ 指标趋势图已保存: {plot_path}")
    
    plt.show()


def generate_report(metrics_data, best_model, best_score, output_dir):
    """生成分析报告"""
    if not metrics_data:
        print("没有数据可分析")
        return
    
    report_path = os.path.join(output_dir, 'training_analysis_report.txt')
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("BUS到CEUS训练指标分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        # 基本统计
        f.write(f"总评估次数: {len(metrics_data)}\n")
        f.write(f"训练步数范围: {metrics_data[0]['step']} - {metrics_data[-1]['step']}\n\n")
        
        # 最佳模型
        if best_model:
            f.write("最佳模型 (综合评分最高):\n")
            f.write(f"  步数: {best_model['step']}\n")
            f.write(f"  SSIM: {best_model['ssim_mean']:.4f} ± {best_model['ssim_std']:.4f}\n")
            f.write(f"  PSNR: {best_model['psnr_mean']:.2f} ± {best_model['psnr_std']:.2f} dB\n")
            f.write(f"  综合评分: {best_score:.4f}\n\n")
        
        # 指标统计
        ssim_values = [d['ssim_mean'] for d in metrics_data]
        psnr_values = [d['psnr_mean'] for d in metrics_data]
        
        f.write("SSIM统计:\n")
        f.write(f"  最大值: {max(ssim_values):.4f}\n")
        f.write(f"  最小值: {min(ssim_values):.4f}\n")
        f.write(f"  平均值: {np.mean(ssim_values):.4f}\n")
        f.write(f"  标准差: {np.std(ssim_values):.4f}\n\n")
        
        f.write("PSNR统计:\n")
        f.write(f"  最大值: {max(psnr_values):.2f} dB\n")
        f.write(f"  最小值: {min(psnr_values):.2f} dB\n")
        f.write(f"  平均值: {np.mean(psnr_values):.2f} dB\n")
        f.write(f"  标准差: {np.std(psnr_values):.2f} dB\n\n")
        
        # 模型质量评估
        f.write("模型质量评估:\n")
        excellent_count = sum(1 for d in metrics_data if d['ssim_mean'] > 0.75 and d['psnr_mean'] > 27)
        good_count = sum(1 for d in metrics_data if d['ssim_mean'] > 0.70 and d['psnr_mean'] > 25)
        
        f.write(f"  优秀模型 (SSIM>0.75, PSNR>27dB): {excellent_count}/{len(metrics_data)}\n")
        f.write(f"  良好模型 (SSIM>0.70, PSNR>25dB): {good_count}/{len(metrics_data)}\n")
        
        # 训练建议
        f.write("\n训练建议:\n")
        if best_model['ssim_mean'] > 0.75:
            f.write("  ✅ SSIM表现优秀，结构保持良好\n")
        elif best_model['ssim_mean'] > 0.70:
            f.write("  ⚠️  SSIM表现一般，可考虑调整超参数\n")
        else:
            f.write("  ❌ SSIM表现较差，建议检查数据质量或模型配置\n")
        
        if best_model['psnr_mean'] > 27:
            f.write("  ✅ PSNR表现优秀，图像质量良好\n")
        elif best_model['psnr_mean'] > 25:
            f.write("  ⚠️  PSNR表现一般，可考虑调整生成参数\n")
        else:
            f.write("  ❌ PSNR表现较差，建议检查训练配置\n")
    
    print(f"✅ 分析报告已保存: {report_path}")


def main():
    parser = argparse.ArgumentParser(description="分析BUS到CEUS训练指标")
    parser.add_argument("--checkpoint_dir", type=str, default="checkpoints/bus_to_ceus_stage2",
                       help="检查点目录")
    parser.add_argument("--output_dir", type=str, default="analysis_results",
                       help="分析结果输出目录")
    parser.add_argument("--plot", action="store_true", default=True,
                       help="生成趋势图")
    parser.add_argument("--report", action="store_true", default=True,
                       help="生成分析报告")
    
    args = parser.parse_args()
    
    # 检查输入目录
    if not os.path.exists(args.checkpoint_dir):
        print(f"❌ 检查点目录不存在: {args.checkpoint_dir}")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"🔍 分析检查点目录: {args.checkpoint_dir}")
    
    # 收集指标数据
    metrics_data = collect_all_metrics(args.checkpoint_dir)
    
    if not metrics_data:
        print("❌ 没有找到有效的指标数据")
        return
    
    print(f"✅ 找到 {len(metrics_data)} 个评估结果")
    
    # 找到最佳模型
    best_model, best_score = find_best_model(metrics_data)
    
    if best_model:
        print(f"\n🏆 最佳模型:")
        print(f"   步数: {best_model['step']}")
        print(f"   SSIM: {best_model['ssim_mean']:.4f} ± {best_model['ssim_std']:.4f}")
        print(f"   PSNR: {best_model['psnr_mean']:.2f} ± {best_model['psnr_std']:.2f} dB")
        print(f"   综合评分: {best_score:.4f}")
    
    # 生成图表
    if args.plot:
        try:
            plot_metrics(metrics_data, args.output_dir)
        except ImportError:
            print("⚠️  matplotlib未安装，跳过图表生成")
            print("安装命令: pip install matplotlib")
    
    # 生成报告
    if args.report:
        generate_report(metrics_data, best_model, best_score, args.output_dir)
    
    print(f"\n📊 分析完成！结果保存在: {args.output_dir}")


if __name__ == "__main__":
    main()
