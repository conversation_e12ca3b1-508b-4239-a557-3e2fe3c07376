#!/usr/bin/env python3
"""
MENTOR环境设置脚本

这个脚本将帮助您：
1. 检查系统要求
2. 安装必要的依赖包
3. 下载预训练模型和tokenizer
4. 验证安装

使用方法:
    python setup_environment.py --download_models
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import urllib.request
import zipfile
import tarfile


def check_system_requirements():
    """检查系统要求"""
    print("=== 检查系统要求 ===")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    else:
        print("✅ Python版本满足要求")
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA可用，版本: {torch.version.cuda}")
            print(f"✅ GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("⚠️  CUDA不可用，将使用CPU训练（速度较慢）")
    except ImportError:
        print("⚠️  PyTorch未安装，稍后将安装")
    
    return True


def install_dependencies():
    """安装依赖包"""
    print("\n=== 安装依赖包 ===")
    
    # 检查是否存在environment.yml
    if os.path.exists("environment.yml"):
        print("发现environment.yml文件，使用conda安装环境...")
        try:
            subprocess.run(["conda", "env", "create", "--file", "environment.yml"], check=True)
            print("✅ Conda环境创建成功")
            print("请运行: conda activate mentor")
            return True
        except subprocess.CalledProcessError:
            print("❌ Conda环境创建失败，尝试pip安装...")
        except FileNotFoundError:
            print("❌ 未找到conda命令，尝试pip安装...")
    
    # 使用pip安装基本依赖
    dependencies = [
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "transformers>=4.30.0",
        "accelerate>=0.20.0",
        "datasets>=2.12.0",
        "Pillow>=9.0.0",
        "numpy>=1.21.0",
        "opencv-python>=4.5.0",
        "tqdm>=4.64.0",
        "wandb>=0.15.0",
        "deepspeed>=0.9.0",
        "nltk>=3.8.0",
        "sentencepiece>=0.1.99",
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True)
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {dep} 安装失败")
            return False
    
    print("✅ 所有依赖包安装完成")
    return True


def download_models(download_dir="models"):
    """下载预训练模型"""
    print(f"\n=== 下载预训练模型到 {download_dir} ===")
    
    os.makedirs(download_dir, exist_ok=True)
    
    # 模型下载列表
    models_to_download = [
        {
            "name": "MENTOR预训练模型",
            "url": "https://huggingface.co/BleachNick/Mentor",
            "local_dir": os.path.join(download_dir, "Mentor"),
            "command": ["huggingface-cli", "download", "BleachNick/Mentor", "--local-dir"]
        },
        {
            "name": "BLIP2-FlanT5-XL",
            "url": "https://huggingface.co/Salesforce/blip2-flan-t5-xl",
            "local_dir": os.path.join(download_dir, "blip2-flan-t5-xl"),
            "command": ["huggingface-cli", "download", "Salesforce/blip2-flan-t5-xl", "--local-dir"]
        },
        {
            "name": "CLIP-ViT-Large-Patch14",
            "url": "https://huggingface.co/openai/clip-vit-large-patch14",
            "local_dir": os.path.join(download_dir, "clip-vit-large-patch14"),
            "command": ["huggingface-cli", "download", "openai/clip-vit-large-patch14", "--local-dir"]
        }
    ]
    
    # 检查huggingface-cli是否可用
    try:
        subprocess.run(["huggingface-cli", "--help"], capture_output=True, check=True)
        print("✅ huggingface-cli 可用")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ huggingface-cli 不可用，正在安装...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "huggingface_hub[cli]"], check=True)
            print("✅ huggingface-cli 安装成功")
        except subprocess.CalledProcessError:
            print("❌ huggingface-cli 安装失败")
            return False
    
    # 下载模型
    for model in models_to_download:
        print(f"\n下载 {model['name']}...")
        if os.path.exists(model['local_dir']) and os.listdir(model['local_dir']):
            print(f"✅ {model['name']} 已存在，跳过下载")
            continue
        
        try:
            cmd = model['command'] + [model['local_dir']]
            subprocess.run(cmd, check=True)
            print(f"✅ {model['name']} 下载成功")
        except subprocess.CalledProcessError:
            print(f"❌ {model['name']} 下载失败")
            print(f"请手动下载: {model['url']}")
    
    return True


def create_directory_structure():
    """创建必要的目录结构"""
    print("\n=== 创建目录结构 ===")
    
    directories = [
        "data",
        "checkpoints",
        "outputs",
        "logs",
        "models",
    ]
    
    for dir_name in directories:
        os.makedirs(dir_name, exist_ok=True)
        print(f"✅ 创建目录: {dir_name}")


def verify_installation():
    """验证安装"""
    print("\n=== 验证安装 ===")
    
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        
        import transformers
        print(f"✅ Transformers版本: {transformers.__version__}")
        
        from PIL import Image
        print("✅ PIL可用")
        
        import cv2
        print(f"✅ OpenCV版本: {cv2.__version__}")
        
        # 检查CUDA
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.version.cuda}")
        else:
            print("⚠️  CUDA不可用")
        
        print("✅ 所有依赖包验证成功")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="设置MENTOR训练环境")
    parser.add_argument("--download_models", action="store_true", 
                       help="下载预训练模型")
    parser.add_argument("--model_dir", type=str, default="models",
                       help="模型下载目录")
    parser.add_argument("--skip_deps", action="store_true",
                       help="跳过依赖安装")
    
    args = parser.parse_args()
    
    print("🚀 MENTOR环境设置开始")
    
    # 检查系统要求
    if not check_system_requirements():
        print("❌ 系统要求检查失败")
        return
    
    # 创建目录结构
    create_directory_structure()
    
    # 安装依赖
    if not args.skip_deps:
        if not install_dependencies():
            print("❌ 依赖安装失败")
            return
    
    # 下载模型
    if args.download_models:
        if not download_models(args.model_dir):
            print("❌ 模型下载失败")
            return
    
    # 验证安装
    if verify_installation():
        print("\n🎉 环境设置完成！")
        print("\n下一步:")
        print("1. 准备您的BUS和CEUS数据")
        print("2. 运行: python prepare_bus_ceus_data.py --train_dir train --output_dir data")
        print("3. 开始训练")
    else:
        print("❌ 环境验证失败")


if __name__ == "__main__":
    main()
