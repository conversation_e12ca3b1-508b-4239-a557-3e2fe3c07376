# BUS到CEUS训练中的SSIM和PSNR指标指南

本指南说明如何在Stage 2训练过程中启用SSIM和PSNR指标的实时计算和输出。

## 📊 功能概述

### 新增功能
- **实时SSIM计算**: 结构相似性指数，评估图像结构保持程度
- **实时PSNR计算**: 峰值信噪比，评估图像重建质量
- **批次级指标**: 每个评估批次的详细指标
- **统计信息**: 均值、标准差、最小值、最大值
- **多种输出**: 控制台、wandb、文件保存

### 指标说明
- **SSIM (0-1)**: 越接近1表示结构相似性越高
- **PSNR (dB)**: 通常20-40dB，越高表示图像质量越好

## 🚀 快速启用

### 方法1: 使用修改后的训练脚本

```bash
# 启用SSIM和PSNR计算
bash train_bus_to_ceus_stage2.sh
```

脚本会自动：
1. 检测`enable_ssim_psnr=true`设置
2. 运行`patch_training_with_metrics.py`修补原始训练代码
3. 安装必要的依赖包
4. 在训练中启用指标计算

### 方法2: 手动修补

```bash
# 1. 运行修补脚本
python patch_training_with_metrics.py

# 2. 手动启动训练并添加参数
torchrun --nproc_per_node=8 autoregressive/train/train_t2i.py \
    --enable_ssim_psnr \
    --metrics_log_freq 50 \
    [其他训练参数...]
```

## 📋 配置参数

### 训练脚本参数

在`train_bus_to_ceus_stage2.sh`中可以配置：

```bash
# 图像质量评估参数
enable_ssim_psnr=true    # 启用SSIM和PSNR计算
metrics_log_freq=50      # 每50个batch输出一次中间指标
```

### 命令行参数

```bash
--enable_ssim_psnr       # 启用SSIM和PSNR计算
--metrics_log_freq 50    # 指标记录频率
```

## 📊 输出格式

### 控制台输出

```
(step=0000300) EVAL SSIM: 0.7234±0.0456, PSNR: 28.45±3.21
Eval progress: 10/100, Current SSIM: 0.7156, Current PSNR: 27.89
(step=0000300) EVAL SSIM: 0.7189±0.0423, PSNR: 28.12±3.05
```

### Wandb记录

指标会自动记录到wandb，包括：
- `eval_ssim_mean`: SSIM均值
- `eval_ssim_std`: SSIM标准差
- `eval_psnr_mean`: PSNR均值
- `eval_psnr_std`: PSNR标准差
- `eval_ssim_min/max`: SSIM最小/最大值
- `eval_psnr_min/max`: PSNR最小/最大值

### 文件输出

每次评估会生成详细的指标文件：

```
checkpoints/bus_to_ceus_stage2/eval_step_300/
├── metrics_batch_0.txt
├── metrics_batch_1.txt
└── metrics_step_300.txt
```

**metrics_step_300.txt示例**:
```
Evaluation Metrics for Step 300
==================================================
SSIM - Mean: 0.7234, Std: 0.0456
SSIM - Min: 0.6123, Max: 0.8567
PSNR - Mean: 28.45, Std: 3.21
PSNR - Min: 22.34, Max: 35.67

Detailed Scores:
Image_ID,SSIM,PSNR
0,0.7234,28.45
1,0.6789,26.78
...
```

## 🔧 技术实现

### 修补内容

`patch_training_with_metrics.py`会自动修改原始训练脚本：

1. **添加导入**:
   ```python
   import numpy as np
   from skimage.metrics import structural_similarity as ssim
   from skimage.metrics import peak_signal_noise_ratio as psnr
   import cv2
   ```

2. **添加计算函数**:
   - `calculate_ssim_psnr()`: 计算SSIM和PSNR
   - `log_image_metrics()`: 记录指标到日志和wandb

3. **添加命令行参数**:
   - `--enable_ssim_psnr`
   - `--metrics_log_freq`

4. **修改评估循环**: 在图像生成后计算指标

### 依赖包

自动安装的依赖：
- `scikit-image`: SSIM和PSNR计算
- `opencv-python-headless`: 图像处理

## 📈 性能影响

### 计算开销
- **SSIM计算**: 每张图像约1-2ms
- **PSNR计算**: 每张图像约0.5ms
- **总体影响**: 评估时间增加约5-10%

### 内存使用
- 额外内存使用: 约100-200MB
- 主要用于图像格式转换和numpy数组

### 优化建议
1. **减少评估频率**: 增大`eval_steps`参数
2. **减少评估样本**: 减小`max_eval_samples`参数
3. **调整记录频率**: 增大`metrics_log_freq`参数

## 🔍 故障排除

### 常见问题

1. **导入错误**
   ```
   ModuleNotFoundError: No module named 'skimage'
   ```
   **解决**: 运行`pip install scikit-image opencv-python-headless`

2. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   **解决**: 减少`max_eval_samples`或增大`eval_steps`

3. **指标计算失败**
   ```
   SSIM/PSNR计算失败: ...
   ```
   **解决**: 检查图像尺寸和格式，确保生成图像和目标图像匹配

### 调试模式

启用详细日志：
```bash
export PYTHONPATH="$(pwd)":$PYTHONPATH
export CUDA_LAUNCH_BLOCKING=1
python -u autoregressive/train/train_t2i.py --enable_ssim_psnr [其他参数...]
```

## 📊 指标解读

### SSIM指标
- **0.9-1.0**: 优秀，结构高度相似
- **0.8-0.9**: 良好，结构基本保持
- **0.7-0.8**: 一般，有一定结构差异
- **<0.7**: 较差，结构差异明显

### PSNR指标
- **>35dB**: 优秀质量
- **30-35dB**: 良好质量
- **25-30dB**: 可接受质量
- **20-25dB**: 较差质量
- **<20dB**: 质量很差

### 医学图像特殊考虑
- **BUS到CEUS转换**: SSIM通常在0.6-0.8，PSNR在20-30dB
- **对比度变化**: CEUS图像对比度增强可能导致PSNR较低但视觉效果良好
- **结构保持**: SSIM更重要，应重点关注结构相似性

## 🎯 最佳实践

1. **监控趋势**: 关注指标随训练步数的变化趋势
2. **早停策略**: 当SSIM连续下降时考虑停止训练
3. **超参数调优**: 根据指标调整学习率和其他参数
4. **定期评估**: 建议每300-500步评估一次
5. **保存最佳模型**: 根据SSIM+PSNR综合指标保存最佳检查点

---

**注意**: 启用SSIM和PSNR计算会略微增加训练时间，但提供了宝贵的图像质量反馈，有助于监控训练进度和调优模型参数。
