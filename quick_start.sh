#!/bin/bash
# BUS到CEUS转换 - 快速开始脚本
# 这个脚本将引导您完成整个流程

set -e

echo "🚀 BUS到CEUS图像转换 - 快速开始"
echo "=================================="

# 检查是否在正确的目录
if [ ! -f "demo.py" ]; then
    echo "❌ 错误: 请在MENTOR项目根目录下运行此脚本"
    exit 1
fi

# 步骤1: 检查数据目录
echo ""
echo "📁 步骤1: 检查数据目录"
if [ ! -d "train" ]; then
    echo "❌ 错误: 找不到train目录"
    echo "请创建以下目录结构:"
    echo "train/"
    echo "├── BUS/          # B超图像"
    echo "└── CEUS/         # 超声造影图像"
    exit 1
fi

if [ ! -d "train/BUS" ] || [ ! -d "train/CEUS" ]; then
    echo "❌ 错误: train目录下缺少BUS或CEUS子目录"
    echo "请确保目录结构如下:"
    echo "train/"
    echo "├── BUS/          # B超图像"
    echo "└── CEUS/         # 超声造影图像"
    exit 1
fi

# 检查图像数量
bus_count=$(find train/BUS -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" | wc -l)
ceus_count=$(find train/CEUS -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" | wc -l)

echo "✅ 找到BUS图像: $bus_count 张"
echo "✅ 找到CEUS图像: $ceus_count 张"

if [ $bus_count -eq 0 ] || [ $ceus_count -eq 0 ]; then
    echo "❌ 错误: 图像数量不足"
    exit 1
fi

# 步骤2: 设置环境
echo ""
echo "🔧 步骤2: 设置环境"
read -p "是否需要安装依赖和下载模型？(y/n): " install_deps

if [ "$install_deps" = "y" ] || [ "$install_deps" = "Y" ]; then
    echo "正在设置环境..."
    python setup_environment.py --download_models
    if [ $? -ne 0 ]; then
        echo "❌ 环境设置失败"
        exit 1
    fi
    echo "✅ 环境设置完成"
else
    echo "⏭️  跳过环境设置"
fi

# 步骤3: 准备数据
echo ""
echo "📊 步骤3: 准备训练数据"
if [ ! -f "data/train_stage1.jsonl" ]; then
    echo "正在转换数据格式..."
    python prepare_bus_ceus_data.py --train_dir train --output_dir data --split_ratio 0.8
    if [ $? -ne 0 ]; then
        echo "❌ 数据准备失败"
        exit 1
    fi
    echo "✅ 数据准备完成"
else
    echo "✅ 训练数据已存在"
fi

# 步骤4: 训练选择
echo ""
echo "🏋️ 步骤4: 模型训练"
echo "训练分为两个阶段:"
echo "  Stage 1: 多模态对齐 (约6-8小时)"
echo "  Stage 2: 指令调优 (约8-12小时)"
echo ""

read -p "是否开始训练？(y/n): " start_training

if [ "$start_training" = "y" ] || [ "$start_training" = "Y" ]; then
    
    # Stage 1训练
    if [ ! -d "checkpoints/bus_to_ceus_stage1" ] || [ -z "$(ls -A checkpoints/bus_to_ceus_stage1 2>/dev/null)" ]; then
        echo ""
        echo "🚀 开始Stage 1训练..."
        chmod +x train_bus_to_ceus_stage1.sh
        bash train_bus_to_ceus_stage1.sh
        
        if [ $? -ne 0 ]; then
            echo "❌ Stage 1训练失败"
            exit 1
        fi
        echo "✅ Stage 1训练完成"
    else
        echo "✅ Stage 1已完成"
    fi
    
    # Stage 2训练
    echo ""
    read -p "是否继续Stage 2训练？(y/n): " continue_stage2
    
    if [ "$continue_stage2" = "y" ] || [ "$continue_stage2" = "Y" ]; then
        echo "🚀 开始Stage 2训练..."
        chmod +x train_bus_to_ceus_stage2.sh
        bash train_bus_to_ceus_stage2.sh
        
        if [ $? -ne 0 ]; then
            echo "❌ Stage 2训练失败"
            exit 1
        fi
        echo "✅ Stage 2训练完成"
    fi
else
    echo "⏭️  跳过训练"
fi

# 步骤5: 测试推理
echo ""
echo "🔮 步骤5: 测试推理"

if [ -d "checkpoints/bus_to_ceus_stage2" ] && [ -n "$(ls -A checkpoints/bus_to_ceus_stage2 2>/dev/null)" ]; then
    read -p "是否测试推理？(y/n): " test_inference
    
    if [ "$test_inference" = "y" ] || [ "$test_inference" = "Y" ]; then
        # 找一张测试图像
        test_image=$(find train/BUS -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" | head -1)
        
        if [ -n "$test_image" ]; then
            echo "使用测试图像: $test_image"
            mkdir -p outputs
            
            python inference_bus_to_ceus.py \
                --input_image "$test_image" \
                --output_image "outputs/test_ceus_output.png" \
                --model_path "checkpoints/bus_to_ceus_stage2"
            
            if [ $? -eq 0 ]; then
                echo "✅ 推理测试成功！"
                echo "输出图像: outputs/test_ceus_output.png"
            else
                echo "❌ 推理测试失败"
            fi
        else
            echo "❌ 找不到测试图像"
        fi
    fi
else
    echo "⚠️  模型尚未训练完成，无法进行推理测试"
fi

# 完成
echo ""
echo "🎉 快速开始流程完成！"
echo ""
echo "📋 总结:"
echo "✅ 数据准备完成"
if [ -d "checkpoints/bus_to_ceus_stage1" ]; then
    echo "✅ Stage 1训练完成"
fi
if [ -d "checkpoints/bus_to_ceus_stage2" ]; then
    echo "✅ Stage 2训练完成"
fi
echo ""
echo "📖 更多信息请参考: BUS_TO_CEUS_GUIDE.md"
echo ""
echo "🔮 使用训练好的模型进行推理:"
echo "python inference_bus_to_ceus.py \\"
echo "    --input_image your_bus_image.png \\"
echo "    --output_image output_ceus.png \\"
echo "    --model_path checkpoints/bus_to_ceus_stage2"
