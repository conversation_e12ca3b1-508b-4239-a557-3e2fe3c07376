#!/usr/bin/env python3
"""
训练脚本补丁工具
自动修改autoregressive/train/train_t2i.py，添加SSIM和PSNR指标计算
"""

import os
import shutil
import re


def backup_original_file():
    """备份原始训练文件"""
    original_file = "autoregressive/train/train_t2i.py"
    backup_file = "autoregressive/train/train_t2i_original.py"
    
    if os.path.exists(original_file):
        if not os.path.exists(backup_file):
            shutil.copy2(original_file, backup_file)
            print(f"✅ 已备份原始文件: {backup_file}")
        else:
            print(f"✅ 备份文件已存在: {backup_file}")
        return True
    else:
        print(f"❌ 找不到原始文件: {original_file}")
        return False


def add_imports():
    """添加必要的导入"""
    imports_code = '''
# 添加SSIM和PSNR计算相关导入
import numpy as np
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
import cv2
'''
    return imports_code


def add_metrics_functions():
    """添加指标计算函数"""
    functions_code = '''
def calculate_ssim_psnr(generated_images, target_images):
    """计算生成图像和目标图像之间的SSIM和PSNR"""
    ssim_scores = []
    psnr_scores = []
    
    # 确保图像在正确的范围内 [0, 1]
    generated_images = torch.clamp(generated_images, 0, 1)
    target_images = torch.clamp(target_images, 0, 1)
    
    # 转换为numpy数组
    gen_np = generated_images.detach().cpu().numpy()
    target_np = target_images.detach().cpu().numpy()
    
    batch_size = gen_np.shape[0]
    
    for i in range(batch_size):
        # 转换为 (H, W, C) 格式
        gen_img = np.transpose(gen_np[i], (1, 2, 0))
        target_img = np.transpose(target_np[i], (1, 2, 0))
        
        # 如果是RGB图像，转换为灰度图像进行SSIM计算
        if gen_img.shape[2] == 3:
            gen_gray = cv2.cvtColor((gen_img * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            target_gray = cv2.cvtColor((target_img * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            ssim_score = ssim(target_gray, gen_gray, data_range=255)
        else:
            gen_gray = (gen_img.squeeze() * 255).astype(np.uint8)
            target_gray = (target_img.squeeze() * 255).astype(np.uint8)
            ssim_score = ssim(target_gray, gen_gray, data_range=255)
        
        # 计算PSNR
        psnr_score = psnr(target_img, gen_img, data_range=1.0)
        
        ssim_scores.append(ssim_score)
        psnr_scores.append(psnr_score)
    
    return ssim_scores, psnr_scores


def log_image_metrics(logger, ssim_scores, psnr_scores, step, prefix="eval"):
    """记录图像质量指标"""
    if len(ssim_scores) == 0 or len(psnr_scores) == 0:
        return
    
    avg_ssim = np.mean(ssim_scores)
    avg_psnr = np.mean(psnr_scores)
    std_ssim = np.std(ssim_scores)
    std_psnr = np.std(psnr_scores)
    
    # 记录到日志
    logger.info(f"(step={step:07d}) {prefix.upper()} SSIM: {avg_ssim:.4f}±{std_ssim:.4f}, PSNR: {avg_psnr:.2f}±{std_psnr:.2f}")
    
    # 记录到wandb
    if torch.distributed.get_rank() == 0:
        wandb_log = {
            f'{prefix}_ssim_mean': avg_ssim,
            f'{prefix}_ssim_std': std_ssim,
            f'{prefix}_psnr_mean': avg_psnr,
            f'{prefix}_psnr_std': std_psnr,
        }
        wandb.log(wandb_log, step=step)

'''
    return functions_code


def add_argparse_options():
    """添加命令行参数"""
    argparse_code = '''
    # 添加SSIM和PSNR相关参数
    parser.add_argument("--enable_ssim_psnr", action="store_true", default=False,
                       help="启用SSIM和PSNR计算")
    parser.add_argument("--metrics_log_freq", type=int, default=50,
                       help="指标记录频率")
'''
    return argparse_code


def add_evaluation_metrics():
    """添加评估指标计算代码"""
    eval_code = '''
                        # 添加SSIM和PSNR计算
                        if hasattr(args, 'enable_ssim_psnr') and args.enable_ssim_psnr:
                            # 归一化图像到 [0, 1] 范围
                            img_samples_normalized = (img_samples + 1) / 2
                            img_samples_normalized = torch.clamp(img_samples_normalized, 0, 1)
                            
                            eval_gt_normalized = (eval_gt_img + 1) / 2
                            eval_gt_normalized = torch.clamp(eval_gt_normalized, 0, 1)
                            
                            # 计算SSIM和PSNR
                            try:
                                batch_ssim, batch_psnr = calculate_ssim_psnr(img_samples_normalized, eval_gt_normalized)
                                
                                # 记录指标
                                if len(batch_ssim) > 0:
                                    log_image_metrics(logger, batch_ssim, batch_psnr, train_steps, f"eval_batch_{eval_idx}")
                                    
                                    # 保存到文件
                                    metrics_file = f"{eval_dir}/metrics_batch_{eval_idx}.txt"
                                    with open(metrics_file, 'w') as f:
                                        f.write(f"Batch {eval_idx} Metrics\\n")
                                        f.write(f"SSIM: {np.mean(batch_ssim):.4f} ± {np.std(batch_ssim):.4f}\\n")
                                        f.write(f"PSNR: {np.mean(batch_psnr):.2f} ± {np.std(batch_psnr):.2f}\\n")
                                        for i, (s, p) in enumerate(zip(batch_ssim, batch_psnr)):
                                            f.write(f"Image {i}: SSIM={s:.4f}, PSNR={p:.2f}\\n")
                                            
                            except Exception as e:
                                logger.warning(f"SSIM/PSNR计算失败: {e}")
'''
    return eval_code


def patch_training_file():
    """修补训练文件"""
    original_file = "autoregressive/train/train_t2i.py"
    
    if not os.path.exists(original_file):
        print(f"❌ 找不到文件: {original_file}")
        return False
    
    # 读取原始文件
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经修补过
    if "calculate_ssim_psnr" in content:
        print("✅ 文件已经包含SSIM/PSNR功能")
        return True
    
    # 添加导入
    import_pattern = r'(import torch\.nn\.functional as F)'
    content = re.sub(import_pattern, r'\1' + add_imports(), content)
    
    # 添加函数定义
    function_pattern = r'(def create_argparser\(\):)'
    content = re.sub(function_pattern, add_metrics_functions() + r'\1', content)
    
    # 添加命令行参数
    argparse_pattern = r'(parser\.add_argument\("--save_total_limit".*?\n)'
    content = re.sub(argparse_pattern, r'\1' + add_argparse_options(), content, flags=re.DOTALL)
    
    # 在评估循环中添加指标计算
    eval_pattern = r'(save_image\(images_to_save, sample_save_path, nrow=img_samples\.shape\[0\], normalize=True\))'
    content = re.sub(eval_pattern, r'\1' + add_evaluation_metrics(), content)
    
    # 写入修改后的文件
    with open(original_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 训练文件已成功修补，添加了SSIM和PSNR计算功能")
    return True


def install_dependencies():
    """安装必要的依赖"""
    import subprocess
    import sys
    
    dependencies = [
        "scikit-image",
        "opencv-python-headless"
    ]
    
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ 已安装: {dep}")
        except subprocess.CalledProcessError:
            print(f"❌ 安装失败: {dep}")


def main():
    """主函数"""
    print("🔧 开始修补训练脚本以支持SSIM和PSNR指标...")
    
    # 1. 备份原始文件
    if not backup_original_file():
        return
    
    # 2. 安装依赖
    print("\n📦 安装必要的依赖包...")
    install_dependencies()
    
    # 3. 修补文件
    print("\n🔨 修补训练文件...")
    if patch_training_file():
        print("\n🎉 修补完成！")
        print("\n📋 使用方法:")
        print("在训练脚本中添加以下参数:")
        print("--enable_ssim_psnr \\")
        print("--metrics_log_freq 50")
        print("\n📊 指标将在每次评估时计算并记录到:")
        print("- 控制台日志")
        print("- wandb")
        print("- 评估目录中的metrics文件")
    else:
        print("❌ 修补失败")


if __name__ == "__main__":
    main()
