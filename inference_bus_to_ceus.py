#!/usr/bin/env python3
"""
BUS到CEUS图像转换推理脚本

使用训练好的MENTOR模型将B超图像转换为超声造影图像

使用方法:
    python inference_bus_to_ceus.py \
        --input_image path/to/bus_image.png \
        --output_image path/to/output_ceus.png \
        --model_path checkpoints/bus_to_ceus_stage2 \
        --prompt "Convert this B-mode ultrasound image to CEUS"

批量处理:
    python inference_bus_to_ceus.py \
        --input_dir path/to/bus_images/ \
        --output_dir path/to/ceus_outputs/ \
        --model_path checkpoints/bus_to_ceus_stage2
"""

import os
import argparse
import torch
from PIL import Image
from pathlib import Path
import glob
from tqdm import tqdm

# 导入MENTOR相关模块
from autoregressive.models.ori_gpt import GPT_models 
from autoregressive.models.empty_fix_gpt import GPT_models as GPT_models_fix_rope
from tokenizer.tokenizer_image.vq_model import VQ_models
from autoregressive.models.generate import generate


def load_model(args, device):
    """加载训练好的模型"""
    print("正在加载模型...")
    
    torch_dtype = getattr(torch, args.torch_dtype)
    latent_size = args.image_size // args.downsample_size

    # 查找最新的检查点
    if os.path.isdir(args.model_path):
        checkpoint_files = glob.glob(os.path.join(args.model_path, "*.pt"))
        if not checkpoint_files:
            raise ValueError(f"在 {args.model_path} 中找不到检查点文件")
        gpt_ckpt = max(checkpoint_files, key=os.path.getmtime)  # 最新的文件
        print(f"使用检查点: {gpt_ckpt}")
    else:
        gpt_ckpt = args.model_path

    # 加载GPT模型
    gpt_cls = GPT_models_fix_rope if args.fixed_version else GPT_models
    gpt = gpt_cls[args.gpt_model](
        vocab_size=args.vocab_size,
        block_size=latent_size ** 2,
        num_classes=args.num_classes,
        cls_token_num=args.cls_token_num,
        model_type=args.gpt_type,
        use_vision_tower=args.use_vision_tower,
        latent_size=latent_size,
        model_name_or_path=args.model_name_or_path,
        mixed_precision=args.mixed_precision,
        image_place_holder=args.image_place_holder,
        processor_path=args.processor_path,
        max_seq_length=args.cls_token_num,
        multimodal_encoder=args.multimodal_encoder,
        mm_vision_tower=args.mm_vision_tower,
    )
    
    # 加载权重
    state = torch.load(gpt_ckpt, map_location="cpu")
    state_dict = state.get("model", state)
    model_dict = gpt.state_dict()
    pretrained_dict = {
        k: v for k, v in state_dict.items()
        if k in model_dict and model_dict[k].shape == v.shape
    }
    model_dict.update(pretrained_dict)
    gpt.load_state_dict(model_dict, strict=False)
    gpt.eval().to(device, dtype=torch_dtype)

    # 加载VQ解码器
    vq = VQ_models[args.vq_model](
        codebook_size=args.codebook_size, 
        codebook_embed_dim=args.codebook_embed_dim
    )
    vq.load_state_dict(torch.load(args.vq_ckpt, map_location="cpu"))
    vq.eval().to(device, dtype=torch_dtype)

    print("✅ 模型加载完成")
    return gpt, vq, latent_size, torch_dtype


def process_single_image(gpt, vq, image_path, prompt, args, device, latent_size, dtype):
    """处理单张图像"""
    
    # 加载图像
    try:
        img = Image.open(image_path).convert("RGB")
        cond_images = [img]
    except Exception as e:
        print(f"错误: 无法加载图像 {image_path}: {e}")
        return None

    # 构建提示词
    full_prompt = prompt.replace(args.image_place_holder, args.image_place_holder * args.placeholder_num)
    
    # 处理多模态输入
    multimodal_inputs = gpt.multimodal_processor(
        images=cond_images,
        text=[full_prompt],
        max_length=args.cls_token_num,
        padding="max_length",
        truncation=True,
        return_attention_mask=True,
        return_tensors="pt",
    )
    
    cond_idx = multimodal_inputs["input_ids"].to(device)
    att_mask = multimodal_inputs["attention_mask"].to(device)
    pixel_values = multimodal_inputs.get("pixel_values")
    
    if pixel_values is not None:
        if pixel_values.ndim == 4:
            pixel_values = pixel_values.unsqueeze(1)
        pixel_values = pixel_values.to(device, dtype=dtype)
        img_mask = torch.ones(pixel_values.shape[:2], dtype=torch.bool).to(device)
    else:
        img_mask = None

    # 生成图像
    with torch.no_grad():
        embs = gpt.get_multmodal_embeddings(
            pixel_values=pixel_values,
            cond_idx=cond_idx,
            cond_idx_mask=att_mask,
            img_mask=img_mask,
            text_input_ids=None,
            text_attention_mask=None,
        )
        
        emb_masks = att_mask
        if not args.no_left_padding:
            new_emb_masks = torch.flip(emb_masks, dims=[-1])
            new_caption_embs = []
            for caption_emb, emb_mask in zip(embs, emb_masks):
                valid_num = int(emb_mask.sum().item())
                new_caption_emb = torch.cat(
                    [caption_emb[valid_num:], caption_emb[:valid_num]]
                )
                new_caption_embs.append(new_caption_emb)
            new_caption_embs = torch.stack(new_caption_embs)
        else:
            new_caption_embs, new_emb_masks = embs, emb_masks

        new_c_indices = new_caption_embs * new_emb_masks[:, :, None]
        c_emb_masks = new_emb_masks
        qzshape = [
            len(new_c_indices),
            args.codebook_embed_dim,
            latent_size,
            latent_size,
        ]

        out_len = latent_size ** 2
        indices = generate(
            gpt,
            new_c_indices,
            out_len,
            c_emb_masks,
            cfg_scale=args.cfg_scale,
            temperature=args.temperature,
            top_k=args.top_k,
            top_p=args.top_p,
            sample_logits=True,
        )
        
        img_tensor = vq.decode_code(indices, qzshape)[0].cpu()
        img_tensor = (img_tensor + 1) / 2
        img_tensor.clamp_(0, 1)

    # 转换为PIL图像
    from torchvision.transforms import ToPILImage
    to_pil = ToPILImage()
    output_image = to_pil(img_tensor)
    
    return output_image


def main():
    parser = argparse.ArgumentParser(description="BUS到CEUS图像转换推理")
    
    # 输入输出参数
    parser.add_argument("--input_image", type=str, help="输入BUS图像路径")
    parser.add_argument("--input_dir", type=str, help="输入BUS图像目录")
    parser.add_argument("--output_image", type=str, help="输出CEUS图像路径")
    parser.add_argument("--output_dir", type=str, help="输出CEUS图像目录")
    parser.add_argument("--model_path", type=str, required=True, help="训练好的模型路径")
    
    # 模型参数
    parser.add_argument("--vq_ckpt", type=str, default="models/Mentor/vq_ds16_t2i.pt")
    parser.add_argument("--gpt_model", type=str, default="GPT-XL")
    parser.add_argument("--vq_model", type=str, default="VQ-16")
    parser.add_argument("--fixed_version", action="store_true", default=True)
    parser.add_argument("--gpt_type", type=str, default="t2i")
    parser.add_argument("--image_size", type=int, default=512)
    parser.add_argument("--downsample_size", type=int, default=16)
    parser.add_argument("--cls_token_num", type=int, default=512)
    parser.add_argument("--multimodal_encoder", type=str, default="llava")
    parser.add_argument("--model_name_or_path", type=str, default="models/blip2-flan-t5-xl")
    parser.add_argument("--processor_path", type=str, default=None)
    parser.add_argument("--mm_vision_tower", type=str, default="openai/clip-vit-large-patch14")
    parser.add_argument("--vocab_size", type=int, default=16384)
    parser.add_argument("--codebook_size", type=int, default=16384)
    parser.add_argument("--codebook_embed_dim", type=int, default=8)
    parser.add_argument("--num_classes", type=int, default=1000)
    parser.add_argument("--torch_dtype", type=str, default="bfloat16")
    parser.add_argument("--mixed_precision", type=str, default="bf16")
    parser.add_argument("--use_vision_tower", action="store_true", default=True)
    
    # 生成参数
    parser.add_argument("--cfg_scale", type=float, default=7.5)
    parser.add_argument("--temperature", type=float, default=0.9)
    parser.add_argument("--top_k", type=int, default=5000)
    parser.add_argument("--top_p", type=float, default=1.0)
    parser.add_argument("--image_place_holder", type=str, default="<image>")
    parser.add_argument("--placeholder_num", type=int, default=32)
    parser.add_argument("--no_left_padding", action="store_true")
    
    # 提示词
    parser.add_argument("--prompt", type=str, 
                       default="Convert this B-mode ultrasound image <image> to contrast-enhanced ultrasound (CEUS).")
    
    args = parser.parse_args()
    
    # 检查输入参数
    if not args.input_image and not args.input_dir:
        parser.error("必须指定 --input_image 或 --input_dir")
    
    if args.input_image and not args.output_image:
        parser.error("指定 --input_image 时必须指定 --output_image")
    
    if args.input_dir and not args.output_dir:
        parser.error("指定 --input_dir 时必须指定 --output_dir")
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载模型
    gpt, vq, latent_size, dtype = load_model(args, device)
    
    # 处理图像
    if args.input_image:
        # 单张图像处理
        print(f"处理图像: {args.input_image}")
        output_image = process_single_image(
            gpt, vq, args.input_image, args.prompt, args, device, latent_size, dtype
        )
        
        if output_image:
            os.makedirs(os.path.dirname(args.output_image), exist_ok=True)
            output_image.save(args.output_image)
            print(f"✅ 输出保存到: {args.output_image}")
        else:
            print("❌ 图像处理失败")
    
    else:
        # 批量处理
        print(f"批量处理目录: {args.input_dir}")
        os.makedirs(args.output_dir, exist_ok=True)
        
        # 获取所有图像文件
        image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.PNG', '*.JPG', '*.JPEG']
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(args.input_dir, ext)))
        
        if not image_files:
            print(f"在 {args.input_dir} 中没有找到图像文件")
            return
        
        print(f"找到 {len(image_files)} 张图像")
        
        # 处理每张图像
        for image_file in tqdm(image_files, desc="处理图像"):
            filename = os.path.basename(image_file)
            name, ext = os.path.splitext(filename)
            output_path = os.path.join(args.output_dir, f"{name}_ceus{ext}")
            
            output_image = process_single_image(
                gpt, vq, image_file, args.prompt, args, device, latent_size, dtype
            )
            
            if output_image:
                output_image.save(output_path)
                print(f"✅ {filename} -> {os.path.basename(output_path)}")
            else:
                print(f"❌ 处理失败: {filename}")
        
        print(f"批量处理完成！输出目录: {args.output_dir}")


if __name__ == "__main__":
    main()
