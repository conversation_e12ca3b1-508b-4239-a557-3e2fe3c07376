#!/usr/bin/env python3
"""
数据准备脚本：将BUS和CEUS图像数据转换为MENTOR训练所需的JSONL格式

使用方法:
    python prepare_bus_ceus_data.py --train_dir train --output_dir data --split_ratio 0.8

数据结构:
    train/
    ├── BUS/          # B超图像（源图像）
    │   ├── image1.png
    │   ├── image2.png
    │   └── ...
    └── CEUS/         # 超声造影图像（目标图像）
        ├── image1.png
        ├── image2.png
        └── ...

输出格式:
    - train_stage1.jsonl: 用于Stage 1训练的数据
    - train_stage2.jsonl: 用于Stage 2训练的数据
    - valid.jsonl: 验证数据
"""

import os
import json
import argparse
from pathlib import Path
import random
from typing import List, Dict, Tu<PERSON>


def get_image_pairs(bus_dir: str, ceus_dir: str) -> List[Tuple[str, str]]:
    """
    获取BUS和CEUS图像对
    
    Args:
        bus_dir: BUS图像目录
        ceus_dir: CEUS图像目录
    
    Returns:
        图像对列表 [(bus_path, ceus_path), ...]
    """
    bus_images = set()
    ceus_images = set()
    
    # 获取所有BUS图像
    for ext in ['*.png', '*.jpg', '*.jpeg']:
        bus_images.update(Path(bus_dir).glob(ext))
        bus_images.update(Path(bus_dir).glob(ext.upper()))
    
    # 获取所有CEUS图像
    for ext in ['*.png', '*.jpg', '*.jpeg']:
        ceus_images.update(Path(ceus_dir).glob(ext))
        ceus_images.update(Path(ceus_dir).glob(ext.upper()))
    
    # 按文件名匹配图像对
    bus_dict = {img.stem: str(img) for img in bus_images}
    ceus_dict = {img.stem: str(img) for img in ceus_images}
    
    pairs = []
    for name in bus_dict:
        if name in ceus_dict:
            pairs.append((bus_dict[name], ceus_dict[name]))
        else:
            print(f"警告: 找不到与 {name} 对应的CEUS图像")
    
    print(f"找到 {len(pairs)} 对匹配的图像")
    return pairs


def create_stage1_data(image_pairs: List[Tuple[str, str]], output_dir: str) -> str:
    """
    创建Stage 1训练数据 (图像重建任务)
    
    Stage 1主要用于多模态对齐，包括图像重建任务
    """
    stage1_data = []
    
    for bus_path, ceus_path in image_pairs:
        # 图像重建任务：从BUS图像重建CEUS图像
        entry = {
            "image_path": ceus_path,  # 目标图像（CEUS）
            "source_image": bus_path,  # 源图像（BUS）
            "input_text": "Convert the B-mode ultrasound image <image> to contrast-enhanced ultrasound (CEUS) image.",
            "generation_only": False,
            "do_recovery": True,  # 启用图像恢复任务
        }
        stage1_data.append(entry)
        
        # 添加反向任务以增强训练
        reverse_entry = {
            "image_path": bus_path,  # 目标图像（BUS）
            "source_image": ceus_path,  # 源图像（CEUS）
            "input_text": "Convert the contrast-enhanced ultrasound (CEUS) image <image> to B-mode ultrasound image.",
            "generation_only": False,
            "do_recovery": True,
        }
        stage1_data.append(reverse_entry)
    
    # 保存Stage 1数据
    output_file = os.path.join(output_dir, "train_stage1.jsonl")
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in stage1_data:
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')
    
    print(f"Stage 1数据已保存到: {output_file} ({len(stage1_data)} 条记录)")
    return output_file


def create_stage2_data(image_pairs: List[Tuple[str, str]], output_dir: str) -> str:
    """
    创建Stage 2训练数据 (指令调优)
    
    Stage 2用于指令调优，包括更复杂的图像转换任务
    """
    stage2_data = []
    
    # 定义多种指令模板
    instruction_templates = [
        "Transform this B-mode ultrasound image <image> into a contrast-enhanced ultrasound (CEUS) image.",
        "Convert the grayscale ultrasound image <image> to show contrast enhancement.",
        "Generate a CEUS image based on this B-mode ultrasound <image>.",
        "Please convert this B-mode ultrasound <image> to contrast-enhanced ultrasound imaging.",
        "Transform this ultrasound image <image> to show contrast agent enhancement.",
        "Convert this B-mode ultrasound <image> into CEUS format.",
        "Generate contrast-enhanced ultrasound from this B-mode image <image>.",
        "Please enhance this ultrasound image <image> with contrast agent visualization.",
    ]
    
    for bus_path, ceus_path in image_pairs:
        # 随机选择指令模板
        instruction = random.choice(instruction_templates)
        
        entry = {
            "image_path": ceus_path,  # 目标图像（CEUS）
            "source_image": bus_path,  # 源图像（BUS）
            "input_text": instruction,
            "generation_only": False,
            "subject_driven": True,  # 启用主题驱动生成
        }
        stage2_data.append(entry)
    
    # 保存Stage 2数据
    output_file = os.path.join(output_dir, "train_stage2.jsonl")
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in stage2_data:
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')
    
    print(f"Stage 2数据已保存到: {output_file} ({len(stage2_data)} 条记录)")
    return output_file


def create_validation_data(image_pairs: List[Tuple[str, str]], output_dir: str) -> str:
    """
    创建验证数据
    """
    val_data = []
    
    for bus_path, ceus_path in image_pairs:
        entry = {
            "image_path": ceus_path,
            "source_image": bus_path,
            "input_text": "Convert this B-mode ultrasound image <image> to contrast-enhanced ultrasound (CEUS).",
            "generation_only": False,
        }
        val_data.append(entry)
    
    # 保存验证数据
    output_file = os.path.join(output_dir, "valid.jsonl")
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in val_data:
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')
    
    print(f"验证数据已保存到: {output_file} ({len(val_data)} 条记录)")
    return output_file


def main():
    parser = argparse.ArgumentParser(description="准备BUS到CEUS转换的训练数据")
    parser.add_argument("--train_dir", type=str, required=True, 
                       help="训练数据目录，包含BUS和CEUS子文件夹")
    parser.add_argument("--output_dir", type=str, default="data", 
                       help="输出目录")
    parser.add_argument("--split_ratio", type=float, default=0.8, 
                       help="训练/验证数据分割比例")
    parser.add_argument("--seed", type=int, default=42, 
                       help="随机种子")
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    
    # 检查输入目录
    bus_dir = os.path.join(args.train_dir, "BUS")
    ceus_dir = os.path.join(args.train_dir, "CEUS")
    
    if not os.path.exists(bus_dir):
        raise ValueError(f"BUS目录不存在: {bus_dir}")
    if not os.path.exists(ceus_dir):
        raise ValueError(f"CEUS目录不存在: {ceus_dir}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取图像对
    image_pairs = get_image_pairs(bus_dir, ceus_dir)
    
    if len(image_pairs) == 0:
        raise ValueError("没有找到匹配的图像对")
    
    # 随机打乱数据
    random.shuffle(image_pairs)
    
    # 分割训练和验证数据
    split_idx = int(len(image_pairs) * args.split_ratio)
    train_pairs = image_pairs[:split_idx]
    val_pairs = image_pairs[split_idx:]
    
    print(f"训练数据: {len(train_pairs)} 对")
    print(f"验证数据: {len(val_pairs)} 对")
    
    # 创建训练数据
    create_stage1_data(train_pairs, args.output_dir)
    create_stage2_data(train_pairs, args.output_dir)
    
    # 创建验证数据
    if val_pairs:
        create_validation_data(val_pairs, args.output_dir)
    
    print("\n数据准备完成！")
    print(f"输出目录: {args.output_dir}")
    print("生成的文件:")
    print("- train_stage1.jsonl: Stage 1训练数据")
    print("- train_stage2.jsonl: Stage 2训练数据")
    if val_pairs:
        print("- valid.jsonl: 验证数据")


if __name__ == "__main__":
    main()
