#!/usr/bin/env python3
"""
数据准备脚本：将BUS和CEUS图像数据转换为MENTOR训练所需的JSONL格式

使用方法:
    python prepare_bus_ceus_data.py --train_dir train --output_dir data --split_ratio 0.8

支持的数据命名格式:
    1. 标准格式: sh-数字_bus_序号.png 和 sh-数字_ceus_序号.png
       例如: sh-001_bus_01.png <-> sh-001_ceus_01.png
    2. 通用格式: 相同文件名的BUS和CEUS图像对

数据结构:
    train/
    ├── BUS/          # B超图像（源图像）
    │   ├── sh-001_bus_01.png
    │   ├── sh-001_bus_02.png
    │   ├── sh-002_bus_01.png
    │   └── ...
    └── CEUS/         # 超声造影图像（目标图像）
        ├── sh-001_ceus_01.png
        ├── sh-001_ceus_02.png
        ├── sh-002_ceus_01.png
        └── ...

输出格式:
    - train_stage1.jsonl: 用于Stage 1训练的数据
    - train_stage2.jsonl: 用于Stage 2训练的数据
    - valid.jsonl: 验证数据
"""

import os
import json
import argparse
from pathlib import Path
import random
from typing import List, Dict, Tuple


def get_image_pairs(bus_dir: str, ceus_dir: str) -> List[Tuple[str, str]]:
    """
    获取BUS和CEUS图像对
    支持命名格式: sh-数字_bus_序号.png 和 sh-数字_ceus_序号.png

    Args:
        bus_dir: BUS图像目录
        ceus_dir: CEUS图像目录

    Returns:
        图像对列表 [(bus_path, ceus_path), ...]
    """
    import re

    bus_images = set()
    ceus_images = set()

    # 获取所有BUS图像
    for ext in ['*.png', '*.jpg', '*.jpeg']:
        bus_images.update(Path(bus_dir).glob(ext))
        bus_images.update(Path(bus_dir).glob(ext.upper()))

    # 获取所有CEUS图像
    for ext in ['*.png', '*.jpg', '*.jpeg']:
        ceus_images.update(Path(ceus_dir).glob(ext))
        ceus_images.update(Path(ceus_dir).glob(ext.upper()))

    # 解析BUS图像，提取匹配键
    bus_dict = {}
    bus_pattern = re.compile(r'sh-(\d+)_bus_(\d+)', re.IGNORECASE)

    for img in bus_images:
        match = bus_pattern.search(img.stem)
        if match:
            # 使用 "数字_序号" 作为匹配键
            key = f"{match.group(1)}_{match.group(2)}"
            bus_dict[key] = str(img)
        else:
            # 如果不匹配新格式，尝试原始匹配方式
            bus_dict[img.stem] = str(img)

    # 解析CEUS图像，提取匹配键
    ceus_dict = {}
    ceus_pattern = re.compile(r'sh-(\d+)_ceus_(\d+)', re.IGNORECASE)

    for img in ceus_images:
        match = ceus_pattern.search(img.stem)
        if match:
            # 使用 "数字_序号" 作为匹配键
            key = f"{match.group(1)}_{match.group(2)}"
            ceus_dict[key] = str(img)
        else:
            # 如果不匹配新格式，尝试原始匹配方式
            ceus_dict[img.stem] = str(img)

    # 匹配图像对
    pairs = []
    matched_keys = set()

    for key in bus_dict:
        if key in ceus_dict:
            pairs.append((bus_dict[key], ceus_dict[key]))
            matched_keys.add(key)
        else:
            print(f"警告: 找不到与 {key} 对应的CEUS图像")

    # 显示匹配统计
    print(f"BUS图像总数: {len(bus_dict)}")
    print(f"CEUS图像总数: {len(ceus_dict)}")
    print(f"成功匹配的图像对: {len(pairs)}")

    # 显示一些匹配示例
    if pairs:
        print("\n匹配示例:")
        for i, (bus_path, ceus_path) in enumerate(pairs[:3]):
            bus_name = Path(bus_path).name
            ceus_name = Path(ceus_path).name
            print(f"  {i+1}. {bus_name} <-> {ceus_name}")
        if len(pairs) > 3:
            print(f"  ... 还有 {len(pairs)-3} 对")

    return pairs


def create_stage1_data(image_pairs: List[Tuple[str, str]], output_dir: str) -> str:
    """
    创建Stage 1训练数据 (图像重建任务)
    
    Stage 1主要用于多模态对齐，包括图像重建任务
    """
    stage1_data = []
    
    for bus_path, ceus_path in image_pairs:
        # 图像重建任务：从BUS图像重建CEUS图像
        entry = {
            "image_path": ceus_path,  # 目标图像（CEUS）
            "source_image": bus_path,  # 源图像（BUS）
            "input_text": "Convert the B-mode ultrasound image <image> to contrast-enhanced ultrasound (CEUS) image.",
            "generation_only": False,
            "do_recovery": True,  # 启用图像恢复任务
        }
        stage1_data.append(entry)
        
        # 添加反向任务以增强训练
        reverse_entry = {
            "image_path": bus_path,  # 目标图像（BUS）
            "source_image": ceus_path,  # 源图像（CEUS）
            "input_text": "Convert the contrast-enhanced ultrasound (CEUS) image <image> to B-mode ultrasound image.",
            "generation_only": False,
            "do_recovery": True,
        }
        stage1_data.append(reverse_entry)
    
    # 保存Stage 1数据
    output_file = os.path.join(output_dir, "train_stage1.jsonl")
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in stage1_data:
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')
    
    print(f"Stage 1数据已保存到: {output_file} ({len(stage1_data)} 条记录)")
    return output_file


def create_stage2_data(image_pairs: List[Tuple[str, str]], output_dir: str) -> str:
    """
    创建Stage 2训练数据 (指令调优)
    
    Stage 2用于指令调优，包括更复杂的图像转换任务
    """
    stage2_data = []
    
    # 定义多种指令模板
    instruction_templates = [
        "Transform this B-mode ultrasound image <image> into a contrast-enhanced ultrasound (CEUS) image.",
        "Convert the grayscale ultrasound image <image> to show contrast enhancement.",
        "Generate a CEUS image based on this B-mode ultrasound <image>.",
        "Please convert this B-mode ultrasound <image> to contrast-enhanced ultrasound imaging.",
        "Transform this ultrasound image <image> to show contrast agent enhancement.",
        "Convert this B-mode ultrasound <image> into CEUS format.",
        "Generate contrast-enhanced ultrasound from this B-mode image <image>.",
        "Please enhance this ultrasound image <image> with contrast agent visualization.",
    ]
    
    for bus_path, ceus_path in image_pairs:
        # 随机选择指令模板
        instruction = random.choice(instruction_templates)
        
        entry = {
            "image_path": ceus_path,  # 目标图像（CEUS）
            "source_image": bus_path,  # 源图像（BUS）
            "input_text": instruction,
            "generation_only": False,
            "subject_driven": True,  # 启用主题驱动生成
        }
        stage2_data.append(entry)
    
    # 保存Stage 2数据
    output_file = os.path.join(output_dir, "train_stage2.jsonl")
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in stage2_data:
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')
    
    print(f"Stage 2数据已保存到: {output_file} ({len(stage2_data)} 条记录)")
    return output_file


def create_validation_data(image_pairs: List[Tuple[str, str]], output_dir: str) -> str:
    """
    创建验证数据
    """
    val_data = []
    
    for bus_path, ceus_path in image_pairs:
        entry = {
            "image_path": ceus_path,
            "source_image": bus_path,
            "input_text": "Convert this B-mode ultrasound image <image> to contrast-enhanced ultrasound (CEUS).",
            "generation_only": False,
        }
        val_data.append(entry)
    
    # 保存验证数据
    output_file = os.path.join(output_dir, "valid.jsonl")
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in val_data:
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')
    
    print(f"验证数据已保存到: {output_file} ({len(val_data)} 条记录)")
    return output_file


def main():
    parser = argparse.ArgumentParser(description="准备BUS到CEUS转换的训练数据")
    parser.add_argument("--train_dir", type=str, required=True, 
                       help="训练数据目录，包含BUS和CEUS子文件夹")
    parser.add_argument("--output_dir", type=str, default="data", 
                       help="输出目录")
    parser.add_argument("--split_ratio", type=float, default=0.8, 
                       help="训练/验证数据分割比例")
    parser.add_argument("--seed", type=int, default=42, 
                       help="随机种子")
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    
    # 检查输入目录
    bus_dir = os.path.join(args.train_dir, "BUS")
    ceus_dir = os.path.join(args.train_dir, "CEUS")
    
    if not os.path.exists(bus_dir):
        raise ValueError(f"BUS目录不存在: {bus_dir}")
    if not os.path.exists(ceus_dir):
        raise ValueError(f"CEUS目录不存在: {ceus_dir}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取图像对
    image_pairs = get_image_pairs(bus_dir, ceus_dir)
    
    if len(image_pairs) == 0:
        raise ValueError("没有找到匹配的图像对")
    
    # 随机打乱数据
    random.shuffle(image_pairs)
    
    # 分割训练和验证数据
    split_idx = int(len(image_pairs) * args.split_ratio)
    train_pairs = image_pairs[:split_idx]
    val_pairs = image_pairs[split_idx:]
    
    print(f"训练数据: {len(train_pairs)} 对")
    print(f"验证数据: {len(val_pairs)} 对")
    
    # 创建训练数据
    create_stage1_data(train_pairs, args.output_dir)
    create_stage2_data(train_pairs, args.output_dir)
    
    # 创建验证数据
    if val_pairs:
        create_validation_data(val_pairs, args.output_dir)
    
    print("\n数据准备完成！")
    print(f"输出目录: {args.output_dir}")
    print("生成的文件:")
    print("- train_stage1.jsonl: Stage 1训练数据")
    print("- train_stage2.jsonl: Stage 2训练数据")
    if val_pairs:
        print("- valid.jsonl: 验证数据")


if __name__ == "__main__":
    main()
