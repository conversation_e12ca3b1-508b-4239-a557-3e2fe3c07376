#!/usr/bin/env python3
"""
测试数据匹配功能
验证BUS和CEUS图像的命名格式匹配是否正确
"""

import os
import re
from pathlib import Path
import argparse


def analyze_naming_patterns(directory, image_type):
    """分析目录中图像的命名模式"""
    print(f"\n📁 分析 {image_type} 目录: {directory}")
    
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return []
    
    # 获取所有图像文件
    images = []
    for ext in ['*.png', '*.jpg', '*.jpeg']:
        images.extend(Path(directory).glob(ext))
        images.extend(Path(directory).glob(ext.upper()))
    
    print(f"📊 找到 {len(images)} 个图像文件")
    
    # 分析命名模式
    patterns = {
        'sh_format': [],  # sh-数字_type_序号.png
        'other': []       # 其他格式
    }
    
    sh_pattern = re.compile(r'sh-(\d+)_(bus|ceus)_(\d+)', re.IGNORECASE)
    
    for img in images:
        filename = img.name
        match = sh_pattern.search(filename)
        
        if match:
            number = match.group(1)
            img_type = match.group(2).lower()
            sequence = match.group(3)
            patterns['sh_format'].append({
                'filename': filename,
                'number': number,
                'type': img_type,
                'sequence': sequence,
                'key': f"{number}_{sequence}"
            })
        else:
            patterns['other'].append({
                'filename': filename,
                'stem': img.stem
            })
    
    # 显示统计
    print(f"  ✅ sh-数字_type_序号 格式: {len(patterns['sh_format'])} 个")
    print(f"  ⚠️  其他格式: {len(patterns['other'])} 个")
    
    # 显示示例
    if patterns['sh_format']:
        print(f"  📝 sh格式示例:")
        for i, item in enumerate(patterns['sh_format'][:3]):
            print(f"    {i+1}. {item['filename']} -> key: {item['key']}")
        if len(patterns['sh_format']) > 3:
            print(f"    ... 还有 {len(patterns['sh_format'])-3} 个")
    
    if patterns['other']:
        print(f"  📝 其他格式示例:")
        for i, item in enumerate(patterns['other'][:3]):
            print(f"    {i+1}. {item['filename']}")
        if len(patterns['other']) > 3:
            print(f"    ... 还有 {len(patterns['other'])-3} 个")
    
    return patterns


def test_matching(bus_dir, ceus_dir):
    """测试BUS和CEUS图像的匹配"""
    print("\n🔍 测试图像匹配...")
    
    # 分析两个目录的命名模式
    bus_patterns = analyze_naming_patterns(bus_dir, "BUS")
    ceus_patterns = analyze_naming_patterns(ceus_dir, "CEUS")
    
    # 提取匹配键
    bus_keys = {}
    ceus_keys = {}
    
    # 处理sh格式
    for item in bus_patterns['sh_format']:
        bus_keys[item['key']] = item['filename']
    
    for item in ceus_patterns['sh_format']:
        ceus_keys[item['key']] = item['filename']
    
    # 处理其他格式
    for item in bus_patterns['other']:
        bus_keys[item['stem']] = item['filename']
    
    for item in ceus_patterns['other']:
        ceus_keys[item['stem']] = item['filename']
    
    # 找到匹配的对
    matched_pairs = []
    unmatched_bus = []
    unmatched_ceus = []
    
    for key in bus_keys:
        if key in ceus_keys:
            matched_pairs.append((bus_keys[key], ceus_keys[key]))
        else:
            unmatched_bus.append(bus_keys[key])
    
    for key in ceus_keys:
        if key not in bus_keys:
            unmatched_ceus.append(ceus_keys[key])
    
    # 显示匹配结果
    print(f"\n📊 匹配结果:")
    print(f"  ✅ 成功匹配: {len(matched_pairs)} 对")
    print(f"  ❌ 未匹配的BUS图像: {len(unmatched_bus)} 个")
    print(f"  ❌ 未匹配的CEUS图像: {len(unmatched_ceus)} 个")
    
    # 显示匹配示例
    if matched_pairs:
        print(f"\n✅ 匹配示例:")
        for i, (bus_name, ceus_name) in enumerate(matched_pairs[:5]):
            print(f"  {i+1}. {bus_name} <-> {ceus_name}")
        if len(matched_pairs) > 5:
            print(f"  ... 还有 {len(matched_pairs)-5} 对")
    
    # 显示未匹配的文件
    if unmatched_bus:
        print(f"\n❌ 未匹配的BUS图像:")
        for i, filename in enumerate(unmatched_bus[:5]):
            print(f"  {i+1}. {filename}")
        if len(unmatched_bus) > 5:
            print(f"  ... 还有 {len(unmatched_bus)-5} 个")
    
    if unmatched_ceus:
        print(f"\n❌ 未匹配的CEUS图像:")
        for i, filename in enumerate(unmatched_ceus[:5]):
            print(f"  {i+1}. {filename}")
        if len(unmatched_ceus) > 5:
            print(f"  ... 还有 {len(unmatched_ceus)-5} 个")
    
    return len(matched_pairs), len(unmatched_bus), len(unmatched_ceus)


def generate_recommendations(matched_count, unmatched_bus, unmatched_ceus):
    """生成建议"""
    print(f"\n💡 建议:")
    
    if matched_count == 0:
        print("  ❌ 没有找到匹配的图像对！")
        print("  🔧 请检查:")
        print("    1. 文件命名格式是否正确")
        print("    2. BUS和CEUS目录是否包含对应的图像")
        print("    3. 文件扩展名是否一致")
    elif unmatched_bus > 0 or unmatched_ceus > 0:
        print("  ⚠️  存在未匹配的图像")
        print("  🔧 建议:")
        print("    1. 检查文件命名是否一致")
        print("    2. 确认数字和序号部分是否对应")
        print("    3. 检查是否有拼写错误")
    else:
        print("  ✅ 所有图像都成功匹配！")
        print("  🚀 可以开始数据准备")
    
    print(f"\n📋 推荐的命名格式:")
    print("  BUS:  sh-001_bus_01.png, sh-001_bus_02.png, ...")
    print("  CEUS: sh-001_ceus_01.png, sh-001_ceus_02.png, ...")
    print("  其中数字(001)和序号(01)部分必须对应")


def main():
    parser = argparse.ArgumentParser(description="测试BUS和CEUS图像数据的匹配")
    parser.add_argument("--train_dir", type=str, default="train",
                       help="训练数据目录")
    parser.add_argument("--bus_dir", type=str, 
                       help="BUS图像目录 (如果不指定，使用train_dir/BUS)")
    parser.add_argument("--ceus_dir", type=str,
                       help="CEUS图像目录 (如果不指定，使用train_dir/CEUS)")
    
    args = parser.parse_args()
    
    # 确定目录路径
    if args.bus_dir:
        bus_dir = args.bus_dir
    else:
        bus_dir = os.path.join(args.train_dir, "BUS")
    
    if args.ceus_dir:
        ceus_dir = args.ceus_dir
    else:
        ceus_dir = os.path.join(args.train_dir, "CEUS")
    
    print("🔬 BUS到CEUS数据匹配测试")
    print("=" * 50)
    print(f"BUS目录: {bus_dir}")
    print(f"CEUS目录: {ceus_dir}")
    
    # 执行测试
    matched_count, unmatched_bus, unmatched_ceus = test_matching(bus_dir, ceus_dir)
    
    # 生成建议
    generate_recommendations(matched_count, unmatched_bus, unmatched_ceus)
    
    print(f"\n🏁 测试完成")
    print("=" * 50)


if __name__ == "__main__":
    main()
