# BUS到CEUS图像转换使用指南

本指南将帮助您使用MENTOR模型进行B超图像到超声造影图像的转换。

## 📋 目录

1. [环境准备](#环境准备)
2. [数据准备](#数据准备)
3. [模型训练](#模型训练)
4. [SSIM和PSNR指标监控](#ssim和psnr指标监控)
5. [模型推理](#模型推理)
6. [故障排除](#故障排除)

## 🚀 环境准备

### 系统要求

- **Python**: 3.8或更高版本
- **CUDA**: 12.1或更高版本（推荐）
- **GPU**: 8×NVIDIA A100 80GB（训练），1×GPU 24GB+（推理）
- **内存**: 64GB+（推荐）

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/HaozheZhao/MENTOR.git
cd MENTOR
```

2. **设置环境**
```bash
# 自动安装依赖和下载模型
python setup_environment.py --download_models

# 或者手动安装
conda env create --file environment.yml
conda activate mentor
```

3. **验证安装**
```bash
python -c "import torch; print('CUDA available:', torch.cuda.is_available())"
```

## 📁 数据准备

### 数据结构

确保您的数据按以下结构组织：

```
train/
├── BUS/          # B超图像（源图像）
│   ├── image001.png
│   ├── image002.png
│   └── ...
└── CEUS/         # 超声造影图像（目标图像）
    ├── image001.png
    ├── image002.png
    └── ...
```

**重要**: BUS和CEUS文件夹中的图像必须按文件名一一对应。

### 数据转换

运行数据准备脚本：

```bash
python prepare_bus_ceus_data.py \
    --train_dir train \
    --output_dir data \
    --split_ratio 0.8 \
    --seed 42
```

这将生成：
- `data/train_stage1.jsonl`: Stage 1训练数据
- `data/train_stage2.jsonl`: Stage 2训练数据  
- `data/valid.jsonl`: 验证数据

### 数据格式说明

生成的JSONL文件格式：
```json
{
    "image_path": "train/CEUS/image001.png",
    "source_image": "train/BUS/image001.png", 
    "input_text": "Convert this B-mode ultrasound image <image> to CEUS.",
    "generation_only": false,
    "do_recovery": true
}
```

## 🏋️ 模型训练

### Stage 1: 多模态对齐

Stage 1主要用于图像重建和多模态对齐：

```bash
# 确保脚本可执行
chmod +x train_bus_to_ceus_stage1.sh

# 开始训练
bash train_bus_to_ceus_stage1.sh
```

**训练参数说明**:
- `--epochs 3`: 训练3个epoch
- `--global-batch-size 32`: 全局批次大小
- `--lr 5e-4`: 学习率
- `--eval_steps 500`: 每500步评估一次
- `--do_recovery`: 启用图像恢复任务

**预期时间**: 约6-8小时（8×A100）

### Stage 2: 指令调优

Stage 2用于指令调优和高质量生成：

```bash
# 确保Stage 1完成后再运行
chmod +x train_bus_to_ceus_stage2.sh

# 开始训练
bash train_bus_to_ceus_stage2.sh
```

**训练参数说明**:
- `--epochs 5`: 训练5个epoch
- `--global-batch-size 16`: 更小的批次大小
- `--lr 1e-4`: 更小的学习率
- `--subject_driven`: 启用主题驱动生成

**预期时间**: 约8-12小时（8×A100）

### 监控训练

查看训练日志：
```bash
# 实时查看日志
tail -f logs/stage1_*.log
tail -f logs/stage2_*.log

# 查看检查点
ls -la checkpoints/bus_to_ceus_stage1/
ls -la checkpoints/bus_to_ceus_stage2/

# 查看SSIM/PSNR指标文件（Stage 2）
ls -la checkpoints/bus_to_ceus_stage2/*/metrics_*.txt
```

## � SSIM和PSNR指标监控

### 功能概述

Stage 2训练现在支持实时SSIM和PSNR指标计算，帮助您监控图像生成质量：

- **SSIM (结构相似性指数)**: 评估生成图像与目标图像的结构相似性 (0-1，越高越好)
- **PSNR (峰值信噪比)**: 评估图像重建质量 (dB，通常20-40dB，越高越好)

### 启用指标计算

指标计算在Stage 2训练中**默认启用**。如需禁用，可修改`train_bus_to_ceus_stage2.sh`：

```bash
# 在脚本中修改
enable_ssim_psnr=false    # 禁用SSIM和PSNR计算
```

### 指标输出格式

#### 1. 控制台实时输出
```bash
(step=0000300) EVAL SSIM: 0.7234±0.0456, PSNR: 28.45±3.21
Eval progress: 10/100, Current SSIM: 0.7156, Current PSNR: 27.89
(step=0000600) EVAL SSIM: 0.7456±0.0398, PSNR: 29.12±2.87
```

#### 2. Wandb可视化
指标自动记录到wandb，包括：
- `eval_ssim_mean/std`: SSIM均值和标准差
- `eval_psnr_mean/std`: PSNR均值和标准差
- `eval_ssim_min/max`: SSIM最小/最大值
- `eval_psnr_min/max`: PSNR最小/最大值

#### 3. 详细指标文件
```bash
checkpoints/bus_to_ceus_stage2/eval_step_300/
├── metrics_step_300.txt      # 整体统计信息
├── metrics_batch_0.txt       # 每个batch的详细指标
├── metrics_batch_1.txt
└── batch_0_cfg_7.5_topk_16384.jpg  # 生成的对比图像
```

**metrics_step_300.txt示例**:
```
Evaluation Metrics for Step 300
==================================================
SSIM - Mean: 0.7234, Std: 0.0456
SSIM - Min: 0.6123, Max: 0.8567
PSNR - Mean: 28.45, Std: 3.21
PSNR - Min: 22.34, Max: 35.67

Detailed Scores:
Image_ID,SSIM,PSNR
0,0.7234,28.45
1,0.6789,26.78
...
```

### 指标解读

#### SSIM指标 (0-1)
- **0.9-1.0**: 优秀，结构高度相似
- **0.8-0.9**: 良好，结构基本保持
- **0.7-0.8**: 一般，有一定结构差异
- **0.6-0.7**: 较差，结构差异明显
- **<0.6**: 很差，结构严重失真

#### PSNR指标 (dB)
- **>35dB**: 优秀质量
- **30-35dB**: 良好质量
- **25-30dB**: 可接受质量
- **20-25dB**: 较差质量
- **<20dB**: 质量很差

#### BUS到CEUS转换的典型指标
- **SSIM**: 通常在0.6-0.8范围内（由于对比度增强）
- **PSNR**: 通常在20-30dB范围内
- **注意**: CEUS图像的对比度增强可能导致PSNR较低，但视觉效果良好

### 配置参数

在`train_bus_to_ceus_stage2.sh`中可调整：

```bash
enable_ssim_psnr=true    # 启用SSIM和PSNR计算
metrics_log_freq=50      # 每50个batch输出一次中间指标
eval_steps=300           # 每300步进行一次完整评估
```

### 性能影响

- **计算开销**: 评估时间增加约5-10%
- **内存使用**: 额外100-200MB内存
- **存储空间**: 每次评估生成约1-5MB指标文件

### 最佳实践

1. **监控趋势**: 关注SSIM和PSNR随训练步数的变化
2. **早停策略**: 当指标连续下降时考虑停止训练
3. **超参数调优**: 根据指标调整学习率和CFG scale
4. **模型选择**: 保存SSIM+PSNR综合表现最佳的检查点

## �🔮 模型推理

### 单张图像转换

```bash
python inference_bus_to_ceus.py \
    --input_image path/to/bus_image.png \
    --output_image path/to/output_ceus.png \
    --model_path checkpoints/bus_to_ceus_stage2 \
    --prompt "Convert this B-mode ultrasound image <image> to CEUS"
```

### 批量图像转换

```bash
python inference_bus_to_ceus.py \
    --input_dir path/to/bus_images/ \
    --output_dir path/to/ceus_outputs/ \
    --model_path checkpoints/bus_to_ceus_stage2
```

### 自定义提示词

您可以使用不同的提示词来控制生成效果：

```bash
# 基础转换
--prompt "Convert this B-mode ultrasound image <image> to CEUS."

# 强调对比度增强
--prompt "Transform this ultrasound image <image> to show contrast agent enhancement."

# 医学术语
--prompt "Generate contrast-enhanced ultrasound from this B-mode image <image>."
```

### 推理参数调优

- `--cfg_scale 7.5`: 分类器自由引导强度（1.0-15.0）
- `--temperature 0.9`: 采样温度（0.1-2.0）
- `--top_k 5000`: Top-k采样
- `--top_p 1.0`: Top-p采样

## 🔧 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减少批次大小
   --global-batch-size 8
   --gradient-accumulation-steps 16

   # 禁用SSIM/PSNR计算以节省内存
   enable_ssim_psnr=false
   ```

2. **找不到检查点文件**
   ```bash
   # 检查文件是否存在
   ls -la models/Mentor/

   # 重新下载模型
   python setup_environment.py --download_models
   ```

3. **数据加载错误**
   ```bash
   # 检查数据格式
   head -n 5 data/train_stage1.jsonl

   # 重新生成数据
   python prepare_bus_ceus_data.py --train_dir train --output_dir data
   ```

4. **训练中断恢复**
   ```bash
   # 从最新检查点恢复
   # 脚本会自动找到最新的检查点文件
   bash train_bus_to_ceus_stage2.sh
   ```

5. **SSIM/PSNR计算错误**
   ```bash
   # 检查依赖是否安装
   pip install scikit-image opencv-python-headless

   # 查看错误日志
   tail -f logs/stage2_*.log | grep -i "ssim\|psnr\|error"

   # 临时禁用指标计算
   enable_ssim_psnr=false
   ```

6. **指标文件缺失**
   ```bash
   # 检查评估目录
   ls -la checkpoints/bus_to_ceus_stage2/eval_step_*/

   # 确认指标计算已启用
   grep "enable_ssim_psnr" train_bus_to_ceus_stage2.sh
   ```

### 性能优化

1. **减少GPU使用**
   ```bash
   # 修改脚本中的GPU数量
   export CUDA_VISIBLE_DEVICES=0,1,2,3
   nproc_per_node=4
   ```

2. **加速训练**
   ```bash
   # 使用混合精度
   --mixed_precision bf16
   
   # 减少评估频率
   --eval_steps 1000
   ```

3. **内存优化**
   ```bash
   # 启用梯度检查点
   --gradient_checkpointing
   
   # 减少工作进程
   --num_workers 2
   ```

## 📊 评估指标

训练过程中会自动计算以下指标：

### Stage 1指标
- **重建损失**: 图像重建质量
- **感知损失**: 视觉质量评估
- **LPIPS**: 感知相似度
- **FID**: Fréchet Inception Distance

### Stage 2指标（新增）
- **SSIM**: 结构相似性指数 (0-1，越高越好)
- **PSNR**: 峰值信噪比 (dB，越高越好)
- **实时统计**: 均值、标准差、最小值、最大值
- **批次级监控**: 每个评估批次的详细指标

## 🎯 最佳实践

1. **数据质量**: 确保BUS和CEUS图像对齐良好
2. **数据量**: 建议至少1000对图像用于训练
3. **预处理**: 统一图像尺寸和格式
4. **指标监控**:
   - 关注SSIM和PSNR趋势变化
   - SSIM > 0.7 且 PSNR > 25dB 为良好表现
   - 当指标连续下降时考虑早停
5. **超参数调优**:
   - 根据SSIM/PSNR指标调整学习率
   - CFG scale影响生成质量，建议7.5-10.0
   - 根据指标表现选择最佳检查点
6. **验证策略**:
   - 每300-500步评估一次
   - 保存SSIM+PSNR综合表现最佳的模型
   - 定期查看生成的对比图像

## � 指标分析和模型选择

### 如何解读训练指标

#### 理想的指标趋势
```
Step 300:  SSIM: 0.65±0.08, PSNR: 22.3±4.2  # 训练初期
Step 600:  SSIM: 0.71±0.06, PSNR: 25.1±3.8  # 逐步改善
Step 900:  SSIM: 0.76±0.05, PSNR: 27.8±3.2  # 稳定提升
Step 1200: SSIM: 0.78±0.04, PSNR: 28.9±2.9  # 接近收敛
```

#### 异常情况识别
- **SSIM突然下降**: 可能过拟合，考虑降低学习率
- **PSNR波动很大**: 批次大小可能过小，增加batch size
- **指标停滞不前**: 学习率可能过低，或已达到模型极限

### 模型选择策略

#### 综合评分方法
```python
# 建议的综合评分公式
score = 0.6 * SSIM + 0.4 * (PSNR / 40.0)
# SSIM权重更高，因为结构保持对医学图像更重要
```

#### 选择最佳检查点
1. **查看所有评估结果**:
   ```bash
   find checkpoints/bus_to_ceus_stage2 -name "metrics_step_*.txt" | sort
   ```

2. **提取关键指标**:
   ```bash
   grep "SSIM - Mean\|PSNR - Mean" checkpoints/bus_to_ceus_stage2/*/metrics_step_*.txt
   ```

3. **选择标准**:
   - SSIM > 0.75 且 PSNR > 27dB: 优秀模型
   - SSIM > 0.70 且 PSNR > 25dB: 良好模型
   - SSIM < 0.65 或 PSNR < 22dB: 需要继续训练

### 实时监控建议

#### 训练期间监控命令
```bash
# 实时查看指标
tail -f logs/stage2_*.log | grep -E "(step=.*EVAL|SSIM|PSNR)"

# 监控wandb（如果配置）
# 在浏览器中查看实时图表

# 定期检查最新指标
watch -n 30 'find checkpoints/bus_to_ceus_stage2 -name "metrics_step_*.txt" | tail -1 | xargs tail -5'
```

#### 早停决策
- 连续3次评估SSIM下降 > 0.02: 考虑停止
- PSNR连续下降且SSIM无改善: 可能过拟合
- 指标波动过大: 调整学习率或批次大小

## �📞 技术支持

如果遇到问题，请：
1. 检查日志文件中的错误信息
2. 确认数据格式和路径正确
3. 验证GPU内存和计算能力
4. 查看SSIM/PSNR指标是否正常计算
5. 参考`SSIM_PSNR_GUIDE.md`获取详细指标说明
6. 参考原始MENTOR项目文档

## 🚀 快速开始

如果您想快速开始，可以运行：

```bash
# 1. 设置环境
python setup_environment.py --download_models

# 2. 准备数据（假设您的数据在train/目录下）
python prepare_bus_ceus_data.py --train_dir train --output_dir data

# 3. 开始训练
bash train_bus_to_ceus_stage1.sh
# 等待Stage 1完成后
bash train_bus_to_ceus_stage2.sh  # 自动启用SSIM/PSNR监控

# 4. 监控训练进度
tail -f logs/stage2_*.log | grep -E "SSIM|PSNR|step="

# 5. 查看指标文件
ls -la checkpoints/bus_to_ceus_stage2/*/metrics_*.txt

# 6. 进行推理
python inference_bus_to_ceus.py \
    --input_image test_bus.png \
    --output_image test_ceus.png \
    --model_path checkpoints/bus_to_ceus_stage2
```

---

**祝您使用愉快！** 🎉
