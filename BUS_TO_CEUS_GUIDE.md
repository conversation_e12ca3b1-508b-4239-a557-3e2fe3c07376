# BUS到CEUS图像转换使用指南

本指南将帮助您使用MENTOR模型进行B超图像到超声造影图像的转换。

## 📋 目录

1. [环境准备](#环境准备)
2. [数据准备](#数据准备)
3. [模型训练](#模型训练)
4. [模型推理](#模型推理)
5. [故障排除](#故障排除)

## 🚀 环境准备

### 系统要求

- **Python**: 3.8或更高版本
- **CUDA**: 12.1或更高版本（推荐）
- **GPU**: 8×NVIDIA A100 80GB（训练），1×GPU 24GB+（推理）
- **内存**: 64GB+（推荐）

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/HaozheZhao/MENTOR.git
cd MENTOR
```

2. **设置环境**
```bash
# 自动安装依赖和下载模型
python setup_environment.py --download_models

# 或者手动安装
conda env create --file environment.yml
conda activate mentor
```

3. **验证安装**
```bash
python -c "import torch; print('CUDA available:', torch.cuda.is_available())"
```

## 📁 数据准备

### 数据结构

确保您的数据按以下结构组织：

```
train/
├── BUS/          # B超图像（源图像）
│   ├── image001.png
│   ├── image002.png
│   └── ...
└── CEUS/         # 超声造影图像（目标图像）
    ├── image001.png
    ├── image002.png
    └── ...
```

**重要**: BUS和CEUS文件夹中的图像必须按文件名一一对应。

### 数据转换

运行数据准备脚本：

```bash
python prepare_bus_ceus_data.py \
    --train_dir train \
    --output_dir data \
    --split_ratio 0.8 \
    --seed 42
```

这将生成：
- `data/train_stage1.jsonl`: Stage 1训练数据
- `data/train_stage2.jsonl`: Stage 2训练数据  
- `data/valid.jsonl`: 验证数据

### 数据格式说明

生成的JSONL文件格式：
```json
{
    "image_path": "train/CEUS/image001.png",
    "source_image": "train/BUS/image001.png", 
    "input_text": "Convert this B-mode ultrasound image <image> to CEUS.",
    "generation_only": false,
    "do_recovery": true
}
```

## 🏋️ 模型训练

### Stage 1: 多模态对齐

Stage 1主要用于图像重建和多模态对齐：

```bash
# 确保脚本可执行
chmod +x train_bus_to_ceus_stage1.sh

# 开始训练
bash train_bus_to_ceus_stage1.sh
```

**训练参数说明**:
- `--epochs 3`: 训练3个epoch
- `--global-batch-size 32`: 全局批次大小
- `--lr 5e-4`: 学习率
- `--eval_steps 500`: 每500步评估一次
- `--do_recovery`: 启用图像恢复任务

**预期时间**: 约6-8小时（8×A100）

### Stage 2: 指令调优

Stage 2用于指令调优和高质量生成：

```bash
# 确保Stage 1完成后再运行
chmod +x train_bus_to_ceus_stage2.sh

# 开始训练
bash train_bus_to_ceus_stage2.sh
```

**训练参数说明**:
- `--epochs 5`: 训练5个epoch
- `--global-batch-size 16`: 更小的批次大小
- `--lr 1e-4`: 更小的学习率
- `--subject_driven`: 启用主题驱动生成

**预期时间**: 约8-12小时（8×A100）

### 监控训练

查看训练日志：
```bash
# 实时查看日志
tail -f logs/stage1_*.log
tail -f logs/stage2_*.log

# 查看检查点
ls -la checkpoints/bus_to_ceus_stage1/
ls -la checkpoints/bus_to_ceus_stage2/
```

## 🔮 模型推理

### 单张图像转换

```bash
python inference_bus_to_ceus.py \
    --input_image path/to/bus_image.png \
    --output_image path/to/output_ceus.png \
    --model_path checkpoints/bus_to_ceus_stage2 \
    --prompt "Convert this B-mode ultrasound image <image> to CEUS"
```

### 批量图像转换

```bash
python inference_bus_to_ceus.py \
    --input_dir path/to/bus_images/ \
    --output_dir path/to/ceus_outputs/ \
    --model_path checkpoints/bus_to_ceus_stage2
```

### 自定义提示词

您可以使用不同的提示词来控制生成效果：

```bash
# 基础转换
--prompt "Convert this B-mode ultrasound image <image> to CEUS."

# 强调对比度增强
--prompt "Transform this ultrasound image <image> to show contrast agent enhancement."

# 医学术语
--prompt "Generate contrast-enhanced ultrasound from this B-mode image <image>."
```

### 推理参数调优

- `--cfg_scale 7.5`: 分类器自由引导强度（1.0-15.0）
- `--temperature 0.9`: 采样温度（0.1-2.0）
- `--top_k 5000`: Top-k采样
- `--top_p 1.0`: Top-p采样

## 🔧 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减少批次大小
   --global-batch-size 8
   --gradient-accumulation-steps 16
   ```

2. **找不到检查点文件**
   ```bash
   # 检查文件是否存在
   ls -la models/Mentor/
   
   # 重新下载模型
   python setup_environment.py --download_models
   ```

3. **数据加载错误**
   ```bash
   # 检查数据格式
   head -n 5 data/train_stage1.jsonl
   
   # 重新生成数据
   python prepare_bus_ceus_data.py --train_dir train --output_dir data
   ```

4. **训练中断恢复**
   ```bash
   # 从最新检查点恢复
   # 脚本会自动找到最新的检查点文件
   bash train_bus_to_ceus_stage2.sh
   ```

### 性能优化

1. **减少GPU使用**
   ```bash
   # 修改脚本中的GPU数量
   export CUDA_VISIBLE_DEVICES=0,1,2,3
   nproc_per_node=4
   ```

2. **加速训练**
   ```bash
   # 使用混合精度
   --mixed_precision bf16
   
   # 减少评估频率
   --eval_steps 1000
   ```

3. **内存优化**
   ```bash
   # 启用梯度检查点
   --gradient_checkpointing
   
   # 减少工作进程
   --num_workers 2
   ```

## 📊 评估指标

训练过程中会自动计算以下指标：
- **重建损失**: 图像重建质量
- **感知损失**: 视觉质量评估
- **LPIPS**: 感知相似度
- **FID**: Fréchet Inception Distance

## 🎯 最佳实践

1. **数据质量**: 确保BUS和CEUS图像对齐良好
2. **数据量**: 建议至少1000对图像用于训练
3. **预处理**: 统一图像尺寸和格式
4. **验证**: 定期检查生成质量
5. **超参数**: 根据数据特点调整学习率和批次大小

## 📞 技术支持

如果遇到问题，请：
1. 检查日志文件中的错误信息
2. 确认数据格式和路径正确
3. 验证GPU内存和计算能力
4. 参考原始MENTOR项目文档

## 🚀 快速开始

如果您想快速开始，可以运行：

```bash
# 1. 设置环境
python setup_environment.py --download_models

# 2. 准备数据（假设您的数据在train/目录下）
python prepare_bus_ceus_data.py --train_dir train --output_dir data

# 3. 开始训练
bash train_bus_to_ceus_stage1.sh
# 等待Stage 1完成后
bash train_bus_to_ceus_stage2.sh

# 4. 进行推理
python inference_bus_to_ceus.py \
    --input_image test_bus.png \
    --output_image test_ceus.png \
    --model_path checkpoints/bus_to_ceus_stage2
```

---

**祝您使用愉快！** 🎉
