{"_name_or_path": "google/flan-t5-xl", "architectures": ["T5ForConditionalGeneration"], "classifier_dropout": 0.0, "d_ff": 5120, "d_kv": 64, "d_model": 2048, "decoder_start_token_id": 0, "dense_act_fn": "gelu_new", "dropout_rate": 0.1, "eos_token_id": 1, "feed_forward_proj": "gated-gelu", "freeze_mm_mlp_adapter": false, "image_aspect_ratio": "square", "initializer_factor": 1.0, "is_encoder_decoder": true, "is_gated_act": true, "layer_norm_epsilon": 1e-06, "mm_hidden_size": 1024, "mm_patch_merge_type": "flat", "mm_projector_lr": null, "mm_projector_type": "mlp2x_gelu", "mm_use_im_patch_token": false, "mm_use_im_start_end": false, "mm_vision_select_feature": "patch", "mm_vision_select_layer": -2, "mm_vision_tower": "openai/clip-vit-large-patch14", "model_type": "llava_t5", "n_positions": 512, "num_decoder_layers": 24, "num_heads": 32, "num_layers": 24, "output_past": true, "pad_token_id": 0, "relative_attention_max_distance": 128, "relative_attention_num_buckets": 32, "task_specific_params": {"summarization": {"early_stopping": true, "length_penalty": 2.0, "max_length": 200, "min_length": 30, "no_repeat_ngram_size": 3, "num_beams": 4, "prefix": "summarize: "}, "translation_en_to_de": {"early_stopping": true, "max_length": 300, "num_beams": 4, "prefix": "translate English to German: "}, "translation_en_to_fr": {"early_stopping": true, "max_length": 300, "num_beams": 4, "prefix": "translate English to French: "}, "translation_en_to_ro": {"early_stopping": true, "max_length": 300, "num_beams": 4, "prefix": "translate English to Romanian: "}}, "tie_word_embeddings": false, "tokenizer_model_max_length": 2048, "tokenizer_padding_side": "right", "torch_dtype": "float32", "transformers_version": "4.37.2", "tune_mm_mlp_adapter": true, "use_cache": true, "use_mm_proj": true, "vocab_size": 32128}