#!/bin/bash
# BUS到CEUS转换 - Stage 2训练脚本
# 用于指令调优和高质量图像生成

set -e  # 遇到错误时退出

# 配置GPU
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export PYTHONPATH="$(pwd)":$PYTHONPATH
export NCCL_DEBUG=ERROR

# 实验配置
experiment_name="checkpoints/bus_to_ceus_stage2"
model_name_or_path="models/blip2-flan-t5-xl"

# 数据路径
data_path="data/train_stage2.jsonl"
val_data_path="data/valid.jsonl"

# 模型路径 - 使用Stage 1的输出作为起点
stage1_checkpoint="checkpoints/bus_to_ceus_stage1"  # Stage 1的最佳检查点
vq_ckpt="models/Mentor/vq_ds16_t2i.pt"
language_projection="llava-v1.5-flant5_fixed-pretrain/mm_projector.bin"

# 训练参数
lr=1e-4           # Stage 2使用更小的学习率
num_workers=8
nproc_per_node=8  # 根据您的GPU数量调整
node_rank=0
eval_steps=300    # 更频繁的评估
ckpt_every=300    # 更频繁的保存
multimodal_encoder="llava"

# 图像质量评估参数
enable_ssim_psnr=true    # 启用SSIM和PSNR计算
metrics_log_freq=50      # 每50个batch输出一次中间指标

# 检查必要文件是否存在
echo "检查必要文件..."

if [ ! -f "$data_path" ]; then
    echo "错误: 训练数据文件不存在: $data_path"
    echo "请先运行: python prepare_bus_ceus_data.py --train_dir train --output_dir data"
    exit 1
fi

if [ ! -d "$stage1_checkpoint" ]; then
    echo "错误: Stage 1检查点不存在: $stage1_checkpoint"
    echo "请先运行Stage 1训练: bash train_bus_to_ceus_stage1.sh"
    exit 1
fi

# 查找Stage 1的最新检查点
latest_checkpoint=$(find "$stage1_checkpoint" -name "*.pt" -type f | sort | tail -1)
if [ -z "$latest_checkpoint" ]; then
    echo "错误: 在 $stage1_checkpoint 中找不到检查点文件"
    exit 1
fi

echo "使用Stage 1检查点: $latest_checkpoint"

if [ ! -f "$vq_ckpt" ]; then
    echo "错误: VQ模型不存在: $vq_ckpt"
    echo "请先运行: python setup_environment.py --download_models"
    exit 1
fi

# 创建输出目录
mkdir -p "$experiment_name"
mkdir -p logs

echo "开始Stage 2训练: BUS到CEUS图像转换指令调优"
echo "实验名称: $experiment_name"
echo "训练数据: $data_path"
echo "验证数据: $val_data_path"
echo "起始检查点: $latest_checkpoint"
echo "SSIM/PSNR评估: $enable_ssim_psnr"

# 修补训练脚本以支持SSIM和PSNR
if [ "$enable_ssim_psnr" = true ]; then
    echo "修补训练脚本以支持SSIM和PSNR..."
    python patch_training_with_metrics.py

    if [ $? -ne 0 ]; then
        echo "❌ 训练脚本修补失败"
        exit 1
    fi
    echo "✅ 训练脚本修补完成"
fi

# 启动训练
torchrun \
--nnodes=1 \
--nproc_per_node=$nproc_per_node \
--node_rank=$node_rank \
autoregressive/train/train_t2i.py \
--vq-ckpt "$vq_ckpt" \
--data-path "$data_path" \
--dataset ti2i \
--image-size 512 \
--results-dir "$experiment_name" \
--cloud-save-path checkpoint \
--lr $lr \
--val_data_path "$val_data_path" \
--use_vision_tower \
--model_name_or_path "$model_name_or_path" \
--image_place_holder "<image>" \
--do_eval \
--eval_steps $eval_steps \
--max_eval_samples 50 \
--cfg-scale 7.5 \
--top-k 16384 \
--load_from_checkpoint "$latest_checkpoint" \
--global-batch-size 16 \
--num-workers $num_workers \
--warmup 0.05 \
--gradient-accumulation-steps 8 \
--train_text_encoder \
--ckpt-every $ckpt_every \
--epochs 5 \
--subject_driven \
--multimodal_encoder $multimodal_encoder \
--do_recovery \
--find_unused_parameters \
--cls-token-num 512 \
--dreambench_eval \
--save_total_limit 3 \
--load_language_projection "$language_projection" \
--gpt-ckpt "$latest_checkpoint" \
--mm_vision_tower "openai/clip-vit-large-patch14" \
--train_all \
--load_fixed_llamagen \
--fix 'gpt-empty-fix' \
$([ "$enable_ssim_psnr" = true ] && echo "--enable_ssim_psnr --metrics_log_freq $metrics_log_freq") \
2>&1 | tee "logs/stage2_$(date +%Y%m%d_%H%M%S).log"

echo "Stage 2训练完成！"
echo "最终模型保存在: $experiment_name"
echo "日志保存在: logs/"
echo ""
echo "下一步: 使用训练好的模型进行推理"
echo "python inference_bus_to_ceus.py --model_path $experiment_name --input_image your_bus_image.png"
