name: nlp
channels:
  - conda-forge
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - asttokens=3.0.0=pyhd8ed1ab_1
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - comm=0.2.2=pyhd8ed1ab_1
  - debugpy=1.8.13=py311hfdbb021_0
  - decorator=5.2.1=pyhd8ed1ab_0
  - exceptiongroup=1.2.2=pyhd8ed1ab_1
  - importlib-metadata=8.6.1=pyha770c72_0
  - ipykernel=6.29.5=pyh3099207_0
  - ipython=9.0.2=pyhfb0248b_0
  - ipython_pygments_lexers=1.1.1=pyhd8ed1ab_0
  - jedi=0.19.2=pyhd8ed1ab_1
  - jupyter_client=8.6.3=pyhd8ed1ab_1
  - jupyter_core=5.7.2=pyh31011fe_1
  - krb5=1.21.3=h143b758_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libedit=3.1.20230828=h5eee18b_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc=14.2.0=h767d61c_2
  - libgcc-ng=14.2.0=h69a702a_2
  - libgomp=14.2.0=h767d61c_2
  - libsodium=1.0.20=h4ab18f5_0
  - libstdcxx=14.2.0=h8f9b012_2
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_1
  - openssl=3.4.1=h7b32b05_0
  - packaging=24.2=pyhd8ed1ab_2
  - parso=0.8.4=pyhd8ed1ab_1
  - pexpect=4.9.0=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pip=25.0=py311h06a4308_0
  - platformdirs=4.3.6=pyhd8ed1ab_1
  - prompt-toolkit=3.0.50=pyha770c72_0
  - psutil=7.0.0=py311h9ecbd09_0
  - ptyprocess=0.7.0=pyhd8ed1ab_1
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - pygments=2.19.1=pyhd8ed1ab_0
  - python=3.11.11=he870216_0
  - python-dateutil=2.9.0.post0=pyhff2d567_1
  - python_abi=3.11=2_cp311
  - pyzmq=26.3.0=py311h7deb3e3_0
  - readline=8.2=h5eee18b_0
  - setuptools=75.8.0=py311h06a4308_0
  - six=1.17.0=pyhd8ed1ab_0
  - sqlite=3.45.3=h5eee18b_0
  - stack_data=0.6.3=pyhd8ed1ab_1
  - tk=8.6.14=h39e8969_0
  - tornado=6.4.2=py311h9ecbd09_0
  - traitlets=5.14.3=pyhd8ed1ab_1
  - typing_extensions=4.12.2=pyha770c72_1
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - wheel=0.45.1=py311h06a4308_0
  - xz=5.6.4=h5eee18b_1
  - zeromq=4.3.5=h3b0a872_7
  - zipp=3.21.0=pyhd8ed1ab_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.11.14
      - aiosignal==1.3.2
      - annotated-types==0.7.0
      - anyio==4.9.0
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.3.0
      - async-lru==2.0.5
      - attrs==25.3.0
      - babel==2.17.0
      - beautifulsoup4==4.13.4
      - bleach==6.2.0
      - certifi==2025.1.31
      - cffi==1.17.1
      - charset-normalizer==3.4.1
      - click==8.1.8
      - contourpy==1.3.1
      - cycler==0.12.1
      - datasets==3.4.1
      - defusedxml==0.7.1
      - dill==0.3.8
      - docker-pycreds==0.4.0
      - executing==2.2.0
      - fastjsonschema==2.21.1
      - filelock==3.18.0
      - fonttools==4.56.0
      - fqdn==1.5.1
      - frozenlist==1.5.0
      - fsspec==2024.12.0
      - gitdb==4.0.12
      - gitpython==3.1.44
      - h11==0.16.0
      - hf-transfer==0.1.9
      - httpcore==1.0.9
      - httpx==0.28.1
      - huggingface-hub==0.29.3
      - idna==3.10
      - isoduration==20.11.0
      - jinja2==3.1.4
      - joblib==1.4.2
      - json5==0.12.0
      - jsonpointer==3.0.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2025.4.1
      - jupyter-events==0.12.0
      - jupyter-lsp==2.2.5
      - jupyter-server==2.15.0
      - jupyter-server-terminals==0.5.3
      - jupyterlab==4.4.1
      - jupyterlab-pygments==0.3.0
      - jupyterlab-server==2.27.3
      - kiwisolver==1.4.8
      - markupsafe==2.1.5
      - matplotlib==3.10.1
      - mistune==3.1.3
      - mpmath==1.3.0
      - multidict==6.2.0
      - multiprocess==0.70.16
      - nbclient==0.10.2
      - nbconvert==7.16.6
      - nbformat==5.10.4
      - networkx==3.3
      - nltk==3.9.1
      - notebook==7.4.1
      - notebook-shim==0.2.4
      - numpy==2.2.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==*********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==**********
      - nvidia-cusparse-cu12==**********
      - nvidia-ml-py==12.570.86
      - nvidia-nccl-cu12==2.21.5
      - nvidia-nvjitlink-cu12==12.1.105
      - nvidia-nvtx-cu12==12.1.105
      - nvitop==1.4.2
      - opencv-python==*********
      - overrides==7.7.0
      - pandas==2.2.3
      - pandocfilters==1.5.1
      - pillow==11.1.0
      - prometheus-client==0.21.1
      - propcache==0.3.0
      - protobuf==5.29.3
      - pyarrow==19.0.1
      - pycparser==2.22
      - pydantic==2.10.6
      - pydantic-core==2.27.2
      - pyparsing==3.2.1
      - python-json-logger==3.3.0
      - pytz==2025.1
      - pyyaml==6.0.2
      - referencing==0.36.2
      - regex==2024.11.6
      - requests==2.32.3
      - rfc3339-validator==0.1.4
      - rfc3986-validator==0.1.1
      - rpds-py==0.24.0
      - safetensors==0.5.3
      - send2trash==1.8.3
      - sentry-sdk==2.23.1
      - setproctitle==1.3.5
      - smmap==5.0.2
      - sniffio==1.3.1
      - soupsieve==2.7
      - sympy==1.13.1
      - terminado==0.18.1
      - tinycss2==1.4.0
      - tokenizers==0.21.1
      - torch==2.5.1+cu121
      - torchaudio==2.5.1+cu121
      - torchvision==0.20.1+cu121
      - tqdm==4.67.1
      - transformers==4.49.0
      - triton==3.1.0
      - types-python-dateutil==2.9.0.20241206
      - tzdata==2025.1
      - uri-template==1.3.0
      - urllib3==2.3.0
      - wandb==0.19.8
      - webcolors==24.11.1
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - xxhash==3.5.0
      - yarl==1.18.3
prefix: anaconda3/envs/nlp
